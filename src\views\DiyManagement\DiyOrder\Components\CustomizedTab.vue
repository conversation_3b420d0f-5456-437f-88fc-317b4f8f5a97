<script setup lang="ts">
defineOptions({
  name: 'CustomizedTab'
})

import { getDictLabel } from '@/utils/dict'
const prop = defineProps<{
  orderDetail: {
    orderType: string
    creditStatus: string
    diyOrderEquityList: any[]
    diyOrderDetail: {
      cardFaceDiy: string
      cardNumber: string
      productionPrice: string
      cardNumberPrice: string
    }
    consignee: string
    address: string
    tel: string
  }
}>()

const data = computed(() => {
  return prop.orderDetail
})
const diyOrderDetail = computed(() => {
  return data.value.diyOrderDetail
})
const equity = computed(() => {
  return (
    (prop.orderDetail?.diyOrderEquityList?.length &&
      prop.orderDetail?.diyOrderEquityList
        .map((item) => {
          return item?.equityName
        })
        .join('；')) ||
    ''
  )
})
const equityPrice = computed(() => {
  return (
    prop.orderDetail?.diyOrderEquityList?.length &&
    prop.orderDetail?.diyOrderEquityList
      .map((item) => {
        return item?.equityPrice
      })
      .join('、')
  )
})
const type = computed(() => {
  const cardFace = prop.orderDetail?.diyOrderDetail?.cardFaceDiy
    ? prop.orderDetail?.diyOrderDetail?.cardFaceDiy?.length
      ? prop.orderDetail?.diyOrderDetail?.cardFaceDiy.join(',')
      : ''
    : ''
  const cardNumber = prop.orderDetail?.diyOrderDetail?.cardNumber
  return cardFace + (cardNumber ? ',卡号' : '') + (equity.value ? `,权益` : '')
})
const tableList = computed<{ label: string; value: string }[][]>(() => [
  [
    {
      label: '订单类型',
      value: getDictLabel('order_diy_order_type', data?.value.orderType)
    },
    {
      label: '银行与网点Code',
      value: '网点-' + data.value.bankBranch
    }
  ],
  [
    {
      label: '图片横竖',
      value: getDictLabel('diy_card_production_mode', diyOrderDetail.value.productionMode)
    },
    {
      label: '卡款类型',
      value: getDictLabel('diy_product_card_type', diyOrderDetail.value.productionType)
    }
  ],
  [
    {
      label: 'logo颜色',
      value: getDictLabel('diy_logo_type', diyOrderDetail.value.logoColor)
    },
    {
      label: '卡产品分类名称',
      value: diyOrderDetail.value.cardTypeName
    }
  ],
  [
    {
      label: '卡种类别',
      value: getDictLabel('diy_authorization_card_type', data.value.cardSort)
    },
    {
      label: '卡等级',
      value: getDictLabel('diy_card_level_type', data.value.cardLevel)
    }
  ],
  [
    {
      label: '卡基颜色',
      value: diyOrderDetail.value.cardBasedColourName
    },
    {
      label: '卡背景',
      value: `${
        diyOrderDetail.value.cardBasedColourCode
          ? `颜色:${diyOrderDetail.value.cardBasedColourCode}`
          : ''
      }  ${
        diyOrderDetail.value.cardBackImageCode
          ? ` 编码:${diyOrderDetail.value.cardBackImageCode} `
          : ''
      }`
    }
  ],
  [
    {
      label: '卡号',
      value: diyOrderDetail.value.cardNumber
    },
    {
      label: '卡号金额',
      value: diyOrderDetail.value?.cardNumberPrice
    }
  ],
  [
    {
      label: '卡权益',
      value: equity.value
    },
    {
      label: '卡权益金额',
      value: equityPrice.value
    }
  ],
  [
    {
      label: '卡面金额',
      value: diyOrderDetail.value?.productionPrice
    },
    {
      label: '待收费类型',
      value: getDictLabel('diy_authorization_charge_type', data.value.settleMethod)
    }
  ],
  [
    {
      label: '预制卡开卡结果',
      value:
        diyOrderDetail.value.cardNoMakeFlag === null
          ? ''
          : diyOrderDetail.value.cardNoMakeFlag == 'Y'
          ? '成功'
          : '失败'
    },
    {
      label: '信审状态',
      value: getDictLabel('diy_credit_status', data?.value.creditStatus)
    }
  ],
  [
    {
      label: '是否加急',
      value: data.value.isUrgent ? '是' : '否'
    },
    {
      label: '是否特批',
      value: data.value.isSpecial ? '是' : '否'
    }
  ],
  [
    {
      label: '是否开票',
      value: data.value.isInvoice ? '是' : '否'
    },
    {
      label: '是否金属卡',
      value: diyOrderDetail.value.isMetal == '1' ? '是' : '否'
    }
  ],

  [
    {
      label: '物流类型',
      value: data.value.expressType
    },
    {
      label: '物流单号',
      value: data.value.mailNo
    }
  ],
  [
    {
      label: '收件信息',
      value:
        data?.value.consignee || data?.value.tel || data?.value.address
          ? `姓名：${data?.value.consignee ?? '暂无数据'}；手机号：${
              data?.value.tel ?? '暂无数据'
            }；收货地址：${data?.value.address ?? '暂无数据'}`
          : ''
    }
  ]
])
</script>
<template>
  <table class="table" cellspacing="0" cellpadding="0" border="0">
    <tbody>
      <tr v-for="(item, index) in tableList" :key="index">
        <template v-for="li in item" :key="li.label">
          <td>{{ li.label }}</td>
          <td> {{ li.value }}</td>
        </template>
      </tr>
      <!-- <tr>
        <td>订单类型</td>
        <td> {{ getDictLabel('order_diy_order_type', data?.orderType) }}</td>
        <td>定制类型</td>
        <td>{{ type }} </td>
      </tr>
      <tr>
        <td>卡基颜色</td>
        <td> {{ data.diyOrderDetail.cardBasedColourName }}</td>
        <td>卡背景颜色</td>
        <td>{{ data.diyOrderDetail.cardBackColourName }} </td>
      </tr>
      <tr>
        <td>卡号</td>
        <td>{{ data?.diyOrderDetail?.cardNumber }}</td>
        <td>卡权益</td>
        <td>{{ equity }}</td>
      </tr>
      <tr>
        <td>卡面金额</td>
        <td>{{ data?.diyOrderDetail?.productionPrice }}</td>
        <td>卡号金额</td>
        <td>{{ data?.diyOrderDetail?.cardNumberPrice }}</td>
      </tr>
      <tr>
        <td>卡权益金额</td>
        <td>{{ equityPrice }}</td>
        <td>信审状态</td>
        <td> {{ getDictLabel('diy_credit_status', data?.creditStatus) }}</td>
      </tr>
      <tr>
        <td>收件信息</td>
        <td colspan="3" v-if="data?.consignee || data?.tel || data?.address">{{
          `姓名：${data?.consignee ?? '暂无数据'}；手机号：${data?.tel ?? '暂无数据'}；收货地址：${
            data?.address ?? '暂无数据'
          }`
        }}</td>
      </tr> -->
    </tbody>
  </table>
</template>

<style lang="less" scoped>
.table,
td {
  border: 1px solid #aaa;
  border-collapse: collapse;
}
.table {
  tr {
    td {
      padding: 10px 0;
      text-align: center;
    }
    & > :nth-child(odd) {
      background-color: #f2f2f2;
      width: 165px;
    }
    & > :nth-child(even) {
      width: 400px;
    }
  }
}
</style>
