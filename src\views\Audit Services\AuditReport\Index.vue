<!--审计报告-->
<template>
  <div
    class="flex !h-[calc(100vh-var(--top-tool-height)-var(--tags-view-height)-var(--logo-height))] flex-row"
  >
    <el-row :gutter="16" style="width: 100%">
      <el-col :span="16">
        <DataSector class="flex-col" ref="DataSectorRef" />
      </el-col>
      <el-col :span="8">
        <DistributionSector class="flex-col" ref="DistributionSectorRef" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="AuditReport" lang="ts">
import DataSector from './components/DataSector.vue'
import DistributionSector from './components/DistributionSector.vue'

import controller from '../common/controller'

import dayjs from 'dayjs'
import { deepClone } from '@/utils/deep'

/**时间段,默认今年以来 */
const timePeriod: Ref<String[]> = ref([
  dayjs(new Date(new Date().getFullYear(), 0)).format('YYYY-MM'),
  dayjs(new Date()).format('YYYY-MM')
])

const DataSectorRef = ref()
const DistributionSectorRef = ref()

const initData = async () => {
  // 获取到所有的套餐和服务与其客户订阅数和业务数
  const list = await controller.getPackageAndCustomerService({
    time: timePeriod.value
  })
  // 将获取到的所有的套餐和服务，关联上客户订阅数和业务数，传给各个组件，以减少首屏请求
  // setTimeout(async () => {
  await DistributionSectorRef.value.getDataFn(deepClone(list))
  await DataSectorRef.value.getFirstShowStepOne(deepClone(list))
  // }, 500)

  // 让套餐业务数据情况稍后获取数据，让订阅客户总量的同比环比优先返回，在视觉层面上感觉数据返回速度较快
  // setTimeout(async () => {
  //   const res = await controller.getPackageAndServiceFirstShow(deepClone(list))
  //   DataSectorRef.value.getFirstShowStepTwo(res)
  // }, 500)
}

onMounted(() => {
  initData()
})
</script>

<style scoped lang="less"></style>
