<template>
  <div
    class="data-sector-chart-1 w-full mr-16px"
    v-loading="businessNumLoading && !statisticsDialogIsShow"
  >
    <div
      class="distribution-sector-chart-1 bg-white p-16px pb-8px"
      style="border-radius: 4px; box-shadow: var(--el-box-shadow-light)"
    >
      <div class="header flex flex-between">
        <div class="header-left flex" style="align-items: center">
          <div class="title mr-8px" style="font-size: 14px">{{ title }}</div>
          <YearSelect ref="YearSelectRef" @get-data="getTimeData" />
        </div>
        <div class="header-right">
          <el-tooltip :content="'测试业务数据总量'">
            <Icon icon="ant-design:info-circle-outlined" />
          </el-tooltip>
        </div>
      </div>
      <div class="sum-num mt-8px pl-8px" style="font-size: 26px">
        {{ businessNum === undefined ? '--' : businessNum }}
        <span class="text-xl">个</span>
      </div>
      <el-tooltip
        :content="businessNum === undefined ? '暂无数据' : '点击查看详情'"
        placement="top"
        :offset="-8"
      >
        <div class="w-100% flex" style="justify-content: center">
          <echart :options="option" :width="'100%'" :height="'70%'" @click="checkDetail" />
        </div>
      </el-tooltip>

      <div class="ratio-warp flex flex-center mt-8px">
        <div
          class="on-year flex flex-center p-4px w-1/2"
          style="margin: 0 16px"
          v-loading="yearSelectLoading"
        >
          <el-dropdown @command="yearSelectFn">
            <el-button link style="color: var(--el-color-primary)">{{
              selectYearProportion?.name
            }}</el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="(item, index) in yearSelectList"
                  :key="index"
                  :command="item"
                  >{{ item.name }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <Icon
            icon="ep:caret-top"
            class="ml-8px mr-4px"
            style="color: #f56c6c"
            v-if="yearRateChangeInfo.change === 'up'"
          />
          <Icon
            icon="ep:caret-bottom"
            class="ml-8px mr-4px"
            style="color: #67c23a"
            v-if="yearRateChangeInfo.change === 'down'"
          />
          <div class="ml-8px mr-4px" v-if="yearRateChangeInfo.change === 'none'"></div>
          <div class="compare-num">
            {{
              yearRateChangeInfo.change === 'up'
                ? '+'
                : yearRateChangeInfo.change === 'down'
                ? '-'
                : ''
            }}{{ yearRateChangeInfo.rate }}
          </div>
        </div>
        <div
          class="on-year flex flex-center p-4px w-1/2"
          style="margin: 0 16px"
          v-loading="qoqSelectLoading"
        >
          <el-dropdown @command="qoqSelectFn">
            <el-button link style="color: var(--el-color-primary)">{{
              selectQoqProportion?.name
            }}</el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="(item, index) in qoqSelectList"
                  :key="index"
                  :command="item"
                  >{{ item.name }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <Icon
            icon="ep:caret-top"
            class="ml-8px mr-4px"
            style="color: #f56c6c"
            v-if="qoqRateChangeInfo.change === 'up'"
          />
          <Icon
            icon="ep:caret-bottom"
            class="ml-8px mr-4px"
            style="color: #67c23a"
            v-if="qoqRateChangeInfo.change === 'down'"
          />
          <div class="ml-8px mr-4px" v-if="qoqRateChangeInfo.change === 'none'"></div>
          <div class="compare-num">
            {{
              qoqRateChangeInfo.change === 'up'
                ? '+'
                : qoqRateChangeInfo.change === 'down'
                ? '-'
                : ''
            }}{{ qoqRateChangeInfo.rate }}
          </div>
        </div>
      </div>
      <statisticsDialog ref="statisticsDialogRef" @change-time="statisticsChangeTime" />
    </div>
  </div>
</template>

<script setup name="DataSector" lang="ts">
import YearSelect from '../Search/YearSelect.vue'
import { generateDarkColors } from '@/utils/generateDarkColors'
import statisticsDialog from '../statisticsDialog.vue'

const props = defineProps({
  searchFn: {
    type: Function,
    default: () => {}
  }
})

const statisticsDialogRef = ref()

const styleData = reactive({
  width: '90%',
  height: '80%'
})

const YearSelectRef = ref()

const echartHeight = computed(() => {
  return window.innerHeight / 6
})

const emits = defineEmits(['changeYear'])

const times = ref()

const businessNum: Ref<number | undefined> = ref(0)

const businessNumLoading = ref(false)

// 获取业务数据总量
const getBusinessSumData = async () => {
  try {
    businessNumLoading.value = true

    let yearRange: string[] = []

    if (times.value) {
      yearRange = [`${parseInt(times.value)}-01`, `${parseInt(times.value)}-12`]
    }

    const res: number | undefined = await controller.getPackageAndCustomerServiceBusinessSum({
      time: yearRange
    })
    businessNum.value = res
  } finally {
    businessNumLoading.value = false
  }
}

const getTimeData = (_year) => {
  times.value = YearSelectRef.value.year
  getBusinessSumData()
}

const getData = async () => {
  await props.searchFn()
}

const allPackageServiceList = ref([])

/**---------------------------------------------- 提示词 start ----------------------------------------------------------*/

import { useUnit } from '../../hooks/useUnit'

const { tipsMap, getTips } = useUnit()

/**---------------------------------------------- 提示词 end ----------------------------------------------------------*/

/**---------------------------------------- 同比环比 start --------------------------------------------------*/
// 计算比率的函数
import { calculateChange } from '../../utils'

import { yearDateRangesMap } from '../../utils/yearDateRange'
import { qoqDateRangesMap } from '../../utils/qoqDateRange'

import controller from '../../../common/controller'

const title = ref('业务数据总量')

// 选中的同比
const selectYearProportion = ref({
  name: '年同比',
  type: 'year'
})

// 同比选中的比率信息
const yearRateChangeInfo = ref({
  change: 'none',
  rate: '0'
})

// 选中的环比
const selectQoqProportion = ref({
  name: '季环比',
  type: 'quarter'
})

// 环比选中的比率信息
const qoqRateChangeInfo = ref({
  change: 'none',
  rate: '0'
})

// 同比选项
const yearSelectList = ref([
  {
    name: '年同比',
    type: 'year'
  },
  {
    name: '半年同比',
    type: 'half'
  },
  {
    name: '季同比',
    type: 'quarter'
  },
  {
    name: '月同比',
    type: 'month'
  }
])

const yearSelectMap = computed(() => {
  return yearSelectList.value.reduce((a, b) => {
    return {
      ...a,
      [b.type]: b
    }
  }, {})
})

// 环比选项
const qoqSelectList = ref([
  {
    name: '季环比',
    type: 'quarter'
  },
  {
    name: '月环比',
    type: 'month'
  },
  {
    name: '半年环比',
    type: 'half'
  }
])

const qoqSelectMap = computed(() => {
  return qoqSelectList.value.reduce((a, b) => {
    return {
      ...a,
      [b.type]: b
    }
  }, {})
})

const yearSelectLoading = ref(false)

// 选择同比
const yearSelectFn = async (_val) => {
  selectYearProportion.value = _val
  console.log(
    `同比选中：${selectYearProportion.value.name}`,
    yearDateRangesMap[selectYearProportion.value.type]
  )
  try {
    // yearSelectLoading.value = true
    const selectedRage = yearDateRangesMap[selectYearProportion.value.type]
    //业务总量
    try {
      const businessAllRes = await Promise.all([
        controller.getPackageAndCustomerServiceBusinessSum({
          time: selectedRage[1]
        }),
        controller.getPackageAndCustomerServiceBusinessSum({
          time: selectedRage[0]
        })
      ])
      console.log('businessAllRes', businessAllRes)
      const pre_num = businessAllRes[0]
      const next_num = businessAllRes[1]
      console.log('pre_num', pre_num)
      console.log('next_num', next_num)
      yearRateChangeInfo.value = calculateChange(pre_num, next_num)
    } catch (e) {
      yearRateChangeInfo.value = {
        change: 'none',
        rate: '数据有误'
      }
    }
  } finally {
    yearSelectLoading.value = false
  }
}

const qoqSelectLoading = ref(false)

// 选择环比
const qoqSelectFn = async (_val) => {
  selectQoqProportion.value = _val
  console.log(
    `环比选中8949${selectQoqProportion.value.name}`,
    qoqDateRangesMap[selectQoqProportion.value.type]
  )
  try {
    // qoqSelectLoading.value = true
    const selectedRage = qoqDateRangesMap[selectQoqProportion.value.type]
    //业务总量
    try {
      const businessAllRes = await Promise.all([
        controller.getPackageAndCustomerServiceBusinessSum({
          time: selectedRage[1]
        }),
        controller.getPackageAndCustomerServiceBusinessSum({
          time: selectedRage[0]
        })
      ])
      console.log('businessAllRes', businessAllRes)
      const pre_num = businessAllRes[0]
      const next_num = businessAllRes[1]
      console.log('pre_num', pre_num)
      console.log('next_num', next_num)
      qoqRateChangeInfo.value = calculateChange(pre_num, next_num)
    } catch (e) {
      qoqRateChangeInfo.value = {
        change: 'none',
        rate: '数据有误'
      }
    }
  } finally {
    qoqSelectLoading.value = false
  }
}

/**---------------------------------------- 同比环比 end --------------------------------------------------*/

const option = computed(() => {
  return {
    // tooltip: {
    //   trigger: 'axis'
    // },
    xAxis: {
      type: 'category',
      data: data.value?.list.map((el) => el.name),
      show: false,
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      show: false
    },
    grid: {
      containLabel: false,
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%'
    },
    series: [
      {
        data: data.value?.list.map((el) => el.value),
        type: 'bar',
        itemStyle: {
          color: 'rgb(152, 97, 179)'
        }
      }
    ]
  }
})

const data = ref()

const getDataFn = () => {
  data.value = {
    sum: 1230,
    list: []
  }
}

import { deepClone } from '@/utils/deep'

const checkDetail = () => {
  const obj = deepClone(option.value)
  obj.yAxis.show = true
  obj.xAxis.show = true
  obj.grid.containLabel = true
  obj.series[0].label = {
    show: true,
    position: 'top'
  }
  statisticsDialogRef.value.open(obj, title.value, {
    type: 'year',
    times: times.value,
    dataType: 'packageSum' // 客户总量 customerSum，套餐业务 package，套餐业务总数 packageSum
  })
}

import { sum_1, sum_2 } from './fakeData'

onMounted(() => {
  // yearSelectLoading.value = true
  // qoqSelectLoading.value = true
  getDataFn()
  getData()
})

import auditApi from '../../../common/api'

const effectivePackSerList: Ref<string[]> = ref([])

const effectivePackSerRecordMap: any = ref({})

const isInit = ref(false)

/**
 * 设置首屏总数
 * @param _list 从上层传过来的，带有各个套餐业务数据的列表，只需要全部加起来就可以获得总量
 * */
const setInitData = (_list) => {
  try {
    const tempPkgSerList = deepClone(_list)
    const list: any = []
    const tempList: any = []
    let busSum = 0
    tempPkgSerList.forEach((el: any) => {
      if (el.business_num) {
        busSum += el.business_num
      }
      if (auditApi.getBusinessNumGeneral[el.code]) {
        tempList.push(`year#${el.code}`)
        tempList.push(`qoq#${el.code}`)
        list.push(el)
      }
    })
    effectivePackSerList.value = tempList
    effectivePackSerRecordMap.value = list.reduce((a, b) => {
      return {
        ...a,
        [b.code]: {
          year: undefined,
          qoq: undefined
        }
      }
    }, {})
    businessNum.value = busSum
    nextTick(() => {
      data.value.list = sum_2
    })
  } finally {
    businessNumLoading.value = false
  }
}

// 检查是否结束了
const checkBusChartIsFinished = (_map) => {
  console.log('_map------------------>', _map)
  for (const key in _map) {
    if (_map[key].year && _map[key].qoq) {
      effectivePackSerList.value.splice(effectivePackSerList.value.indexOf(key), 1)
    }
  }
  if (effectivePackSerList.value.length === 0) {
    try {
      let year_pre_num_temp = 0
      let year_next_num_temp = 0
      for (const key in effectivePackSerRecordMap.value) {
        year_pre_num_temp += effectivePackSerRecordMap.value[key].year.pre_num
        year_next_num_temp += effectivePackSerRecordMap.value[key].year.next_num
      }
      yearRateChangeInfo.value = calculateChange(year_pre_num_temp, year_next_num_temp)
    } catch (e) {
      yearRateChangeInfo.value = {
        change: 'none',
        rate: '数据有误'
      }
    } finally {
      yearSelectLoading.value = false
    }

    try {
      let qoq_pre_num_temp = 0
      let qoq_next_num_temp = 0
      for (const key in effectivePackSerRecordMap.value) {
        qoq_pre_num_temp += effectivePackSerRecordMap.value[key].qoq.pre_num
        qoq_next_num_temp += effectivePackSerRecordMap.value[key].qoq.next_num
      }
      qoqRateChangeInfo.value = calculateChange(qoq_pre_num_temp, qoq_next_num_temp)
    } catch (e) {
      qoqRateChangeInfo.value = {
        change: 'none',
        rate: '数据有误'
      }
    } finally {
      qoqSelectLoading.value = false
    }
  }
}

const isOver = ref(false)

// 检查是否结束了
const checkBusChartIsFinishedNew = () => {
  if (isOver.value) return
  if (effectivePackSerList.value.length === 0) {
    console.log('effectivePackSerRecordMap.value', effectivePackSerRecordMap.value)
    try {
      let year_pre_num_temp = 0
      let year_next_num_temp = 0
      for (const key in effectivePackSerRecordMap.value) {
        year_pre_num_temp += effectivePackSerRecordMap.value[key].year.pre_num
        year_next_num_temp += effectivePackSerRecordMap.value[key].year.next_num
      }
      yearRateChangeInfo.value = calculateChange(year_pre_num_temp, year_next_num_temp)
    } catch (e) {
      yearRateChangeInfo.value = {
        change: 'none',
        rate: '数据有误'
      }
    } finally {
      yearSelectLoading.value = false
    }

    try {
      let qoq_pre_num_temp = 0
      let qoq_next_num_temp = 0
      for (const key in effectivePackSerRecordMap.value) {
        qoq_pre_num_temp += effectivePackSerRecordMap.value[key].qoq.pre_num
        qoq_next_num_temp += effectivePackSerRecordMap.value[key].qoq.next_num
      }
      qoqRateChangeInfo.value = calculateChange(qoq_pre_num_temp, qoq_next_num_temp)
    } catch (e) {
      qoqRateChangeInfo.value = {
        change: 'none',
        rate: '数据有误'
      }
    } finally {
      qoqSelectLoading.value = false
      isOver.value = true
    }
  }
}

// 设置某个套餐的同比
const setBusChartYear = ({ code, pre_num, next_num }) => {
  if (!effectivePackSerRecordMap.value[code]) return
  effectivePackSerRecordMap.value[code].year = {
    pre_num,
    next_num
  }
  effectivePackSerList.value.splice(effectivePackSerList.value.indexOf(`year#${code}`), 1)
  // 属于是原子操作
  // checkBusChartIsFinished(deepClone(effectivePackSerRecordMap.value))
  checkBusChartIsFinishedNew()
}

// 设置某个套餐的环比
const setBusChartQoq = ({ code, pre_num, next_num }) => {
  if (!effectivePackSerRecordMap.value[code]) return
  effectivePackSerRecordMap.value[code].qoq = {
    pre_num,
    next_num
  }
  effectivePackSerList.value.splice(effectivePackSerList.value.indexOf(`qoq#${code}`), 1)
  // checkBusChartIsFinished(deepClone(effectivePackSerRecordMap.value))
  checkBusChartIsFinishedNew()
}

const statisticsChangeTime = (_time) => {
  YearSelectRef.value.year = _time
}

const statisticsDialogIsShow = computed(() => {
  return statisticsDialogRef.value.show || false
})

/**
 * 设置首屏同比环比
 * @param _list 从上层传过来的，带有各个套餐业务数据和同比环比的列表，只需要全部加起来就可以获得总量
 * */
const setInitCompare = (_list) => {
  try {
    let year_pre_num_temp = 0
    let year_next_num_temp = 0

    _list.forEach((el) => {
      if (el.firstShowCompareInfo) {
        year_pre_num_temp += el.firstShowCompareInfo.year_pre
        year_next_num_temp += el.firstShowCompareInfo.year_next
      }
    })
    yearRateChangeInfo.value = calculateChange(year_pre_num_temp, year_next_num_temp)
  } catch (e) {
    yearRateChangeInfo.value = {
      change: 'none',
      rate: '数据有误'
    }
  } finally {
    yearSelectLoading.value = false
  }

  try {
    let qoq_pre_num_temp = 0
    let qoq_next_num_temp = 0
    _list.forEach((el) => {
      if (el.firstShowCompareInfo) {
        qoq_pre_num_temp += el.firstShowCompareInfo.qoq_pre
        qoq_next_num_temp += el.firstShowCompareInfo.qoq_next
      }
    })
    qoqRateChangeInfo.value = calculateChange(qoq_pre_num_temp, qoq_next_num_temp)
  } catch (e) {
    qoqRateChangeInfo.value = {
      change: 'none',
      rate: '数据有误'
    }
  } finally {
    qoqSelectLoading.value = false
  }
}

defineExpose({
  setInitData,
  businessNumLoading,
  setInitCompare,
  setBusChartYear,
  setBusChartQoq
})

onMounted(() => {
  businessNumLoading.value = true
})
</script>

<style scoped lang="less">
.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-center {
  justify-content: center;
  align-items: center;
}
</style>
