/*
 * @Author: hao-jie.chen <EMAIL>
 * @Date: 2025-01-08 11:15:32
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-23 15:01:10
 * @Description:
 */
import request from '@/config/axios'
import envController from '@/controller/envController'
import qs from 'qs'
const prefix = envController.getManageUrl() + '/app-api/sqlexecutor'
//const prefix = 'http://localhost:8006'

/**
 * 获取客户数量
 *
 * @param params 请求参数
 * @returns 返回一个Promise对象，Promise解析为包含客户数量的对象
 */
export const getCustomerCount = (params: {
  companyType: number[]
}): Promise<{ head: number; branch: number; total: number }> => {
  const paramsStr = qs.stringify(params, { arrayFormat: 'repeat' })
  return request.get({ url: `${prefix}/db-query/customer/count?${paramsStr}` })
}
/**
 * 获取客户列表
 *
 * @param params 请求参数
 * @returns 返回一个Promise对象，Promise解析为包含客户列表的对象
 */
export const getCustomerList = (params: {
  deliveryMethod: number[]
}): Promise<{ customerList: any }> => {
  const paramsStr = qs.stringify(params, { arrayFormat: 'repeat' })
  return request.get({ url: `${prefix}/db-query/customer/getCustomerList?${paramsStr}` })
}

/**
 * 获取客户树列表
 *
 * @param params 请求参数
 * @returns 返回一个Promise对象，Promise解析为包含客户列表的对象
 */
export const getCustomerTreeList = (params: {
  /**
   * 客户ID
   */
  customerId?: string
  /**
   * 客户编码
   */
  customerCode?: string
  /**
   * 分行类型
   */
  branchType?: '0' | '1'
  /**
   * 交付方式
   */
  deliveryMethod?: number[]
  /**
   * 客户名称（模糊查询）
   */
  customerName?: string
}): Promise<{
  hasNext: boolean
  hasPrev: boolean
  items: any[]
  page: number
  pageSize: number
  total: number
  totalPages: number
}> => {
  const paramsStr = qs.stringify(params, { arrayFormat: 'repeat' })
  return request.get({ url: `${prefix}/db-query/customer/simple-node-tree-list?${paramsStr}` })
}

interface DataType {
  customerId: string //客户Id
  halfYear: number //半年度
  month: number //月份
  quarter: string //季度
  type: number //间维度类型(时间维度类型 [1-月度、2-季度、3-半年度、4-年度]
  year: number //年度 ，查询 月、季、半年 需要此参数
}

//客户访问次数
export const getDataBoardByCustomerCount = (data?: {
  cycle: string
  isIncludGoldpac: boolean
  companyType: number[]
}): Promise<{ customerCount: number; totalNum: number; pvcount: number }> => {
  return request.post({
    url: `/customer/customer/getDataBoardByCustomerCount`,
    data
  })
}

// 获取客户数量

/**
 * 获取租户数量
 *
 * @returns 返回一个Promise对象，解析值为一个包含head和branch字段的对象，分别表示总行和分行数量
 */
export const getCountTenant = (params: {
  companyType: number[]
}): Promise<{ head: number; branch: number }> => {
  return request.get({ url: '/admin-api/system/tenant/count-tenant', params })
}

// 客户分类
type customerIndustry = {
  /* */
  customerCode: string

  /* */
  customerCount: number

  /*可用值:MONTH,QUARTER,HALFYEAR,YEAR,DAY,WEEK,TOTAL */
  cycle: string

  /* */
  day: string

  /* */
  halfYear: string

  /* */
  industry: number

  /* */
  isEmptyList: boolean

  /* */
  isIncludGoldpac: boolean

  /* */
  month: string

  /* */
  pvcount: number

  /* */
  quarter: string

  /* */
  total: string

  /* */
  totalNum: number

  /* */
  type: string

  /* */
  week: string

  /* */
  year: string
}

export const getBoardDataCustomer = (data: {
  cycle: string
  companyType: number[]
}): Promise<customerIndustry[]> => {
  return request.post({ url: `/customer/customer/getBoardDataCustomer`, data })
}

//SaaS数量

type saasType = {
  /* */
  customerCode: string
  /* */
  customerCount: number
  /*可用值:MONTH,QUARTER,HALFYEAR,YEAR,DAY,WEEK,TOTAL */
  cycle: string

  /* */
  day: string

  /* */
  halfYear: string

  /* */
  industry: number

  /* */
  isEmptyList: boolean

  /* */
  isIncludGoldpac: boolean

  /* */
  month: string

  /* */
  pvcount: number

  /* */
  quarter: string

  /* */
  total: string

  /* */
  totalNum: number

  /* */
  type: string

  /* */
  week: string

  /* */
  year: string
}

/**
 * 获取SaaS数量
 * @returns
 */
export function getDataBoardBySaasAmount(data: { companyType: number[] }): Promise<saasType[]> {
  return request.post({ url: `/customer/customer/getDataBoardBySaasAmount`, data })
}

// 订单部分
// 批卡订单个数和张数
interface IndustryOrderCollectRes {
  industry?: string /*行业 */
  yearMonths?: string /*月份（yyyyMM) */

  orderTimes: number /*订单的次数总和 */

  totalAmount: number /*订单的下单产品数量总和 */
}
// 获取批卡订单个数和批卡订单张数（年度）
export function industryYearOrderCollect(data): Promise<IndustryOrderCollectRes[]> {
  return request.post({ url: `/order/order/collect/search/industryOrder`, data })
}

//  获取批卡订单个数和批卡订单张数(月度)
export function industryMonthOrderCollect(data): Promise<IndustryOrderCollectRes[]> {
  return request.post({ url: `/order/order/collect/search/industryMonthOrder`, data })
}

// 批卡客户排名
interface CustomerOrderRankingCollectRes {
  customerId: string /*客户id */

  customerName: string /*客户名称 */

  totalAmount: number /*订单的下单产品数量总和 */
}
//  获取批卡客户排名
export function customerOrderRankingCollect(data): Promise<CustomerOrderRankingCollectRes> {
  return request.post({ url: `/order/order/collect/search/customerOrderRanking`, data })
}
// 响应接口
export type OrderSourceCollectType = {
  /*订单来源(management：管理端；customer：客户端；sale：销售端) */
  orderSource: string

  /*订单的次数总和 */
  orderTimes: number
}

/**
 * 订单来源统计
 * @param {object} params reqParams
 * @param {number} params.year 年份
 * @returns
 */
export function orderSourceCollect(data: {
  year: number | string
}): Promise<OrderSourceCollectType[]> {
  return request.post({ url: `/order/order/collect/search/orderSource`, data })
}

// 获取年度diy订单个数和张数
export function getDIYYearOrder(data) {
  return request.post({ url: `/diy/board/count/v2/order/total`, data })
}

// 获取月度DIY卡订单个数、张数
export function getDIYOrderRange(data) {
  return request.post({ url: `/diy/board/count/v2/order/total/range`, data })
}

// 获取DIY卡客户排名
export function getDIYCustomerRank(data) {
  return request.post({ url: `/diy/board/count/v2/order/customer/rank`, data })
}
// 个人化订单排名
export const getPersonalOrderRank: any = (data?: any) => {
  return request.post({ url: `/personal-order/collect/ranks`, data })
}

// 批卡产品，架统计当询又(客户产品总数量) top
export const getBatchCardProductTotalCountTotal: any = (params?: any) => {
  return request.get({ url: `/product/product/collect/count`, params })
}
// 统计客户批卡下单数据(批卡业务数量) echat5 (客户产品数量)
export const getBatchCardProductCount: any = (data?: any) => {
  return request.post({ url: `/order/order/collect/search/product`, data })
}
// =################################CDC#####################################################
