<template>
  <div
    class="data-sector-chart-1 w-80% mb-10px"
    v-loading="initData?.loading && !statisticsDialogIsShow"
  >
    <div
      class="distribution-sector-chart-1 bg-white p-16px pb-8px"
      style="border-radius: 4px; box-shadow: var(--el-box-shadow-light)"
    >
      <div class="header flex flex-between">
        <div class="header-left flex" style="align-items: center">
          <div style="position: relative">
            <el-tooltip :content="data?.name">
              <div
                class="title mr-8px"
                style="
                  font-size: 14px;
                  max-width: 130px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                "
                >{{ data?.name }}
              </div>
            </el-tooltip>
            <el-tooltip
              :content="tipsMap[data?.code] || ''"
              v-if="tipsMap[data?.code]"
              placement="right"
            >
              <Icon
                icon="ep:info-filled"
                :size="12"
                style="
                  position: absolute;
                  top: -3px;
                  right: -5px;
                  color: rgb(96, 98, 102);
                  cursor: pointer;
                "
              />
            </el-tooltip>
          </div>
        </div>
        <div class="header-right">
          <TimeSelect ref="TimeSelectRef" @get-data="getTimeData" />
        </div>
      </div>
      <div class="sum-num mt-8px pl-8px" style="font-size: 26px">
        {{ data?.sum === undefined ? '--' : data?.sum }}
        <span class="text-xl" v-if="data?.sum !== undefined && getUnit">{{ getUnit }}</span>
      </div>
      <div class="w-full flex" style="justify-content: center">
        <el-tooltip
          placement="top"
          :offset="-8"
          :content="data?.sum === undefined ? '暂无数据' : '点击查看详情'"
          v-if="data?.sum !== undefined"
        >
          <echart :options="option" :width="'100%'" :height="'70%'" @click="checkDetail" />
        </el-tooltip>
        <echart :options="option" :width="'100%'" :height="'70%'" @click="showNoDataTip" v-else />
      </div>
      <div class="ratio-warp flex flex-center mt-8px">
        <div
          class="on-year flex flex-center p-4px w-1/2"
          style="margin: 0 16px"
          v-loading="yearSelectLoading"
        >
          <el-dropdown @command="yearSelectFn">
            <el-button link style="color: var(--el-color-primary)">{{
              selectYearProportion.name
            }}</el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="(item, index) in yearSelectList"
                  :key="index"
                  :command="item"
                  >{{ item.name }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <Icon
            icon="ep:caret-top"
            class="ml-8px mr-4px"
            style="color: #f56c6c"
            v-if="yearRateChangeInfo.change === 'up'"
          />
          <Icon
            icon="ep:caret-bottom"
            class="ml-8px mr-4px"
            style="color: #67c23a"
            v-if="yearRateChangeInfo.change === 'down'"
          />
          <div class="ml-8px mr-4px" v-if="yearRateChangeInfo.change === 'none'"></div>
          <div class="compare-num">
            {{
              yearRateChangeInfo.change === 'up'
                ? '+'
                : yearRateChangeInfo.change === 'down'
                ? '-'
                : ''
            }}{{ yearRateChangeInfo.rate }}
          </div>
        </div>
        <div
          class="on-year flex flex-center p-4px w-1/2"
          style="margin: 0 16px"
          v-loading="qoqSelectLoading"
        >
          <el-dropdown @command="qoqSelectFn">
            <el-button link style="color: var(--el-color-primary)">{{
              selectQoqProportion.name
            }}</el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="(item, index) in qoqSelectList"
                  :key="index"
                  :command="item"
                  >{{ item.name }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <Icon
            icon="ep:caret-top"
            class="ml-8px mr-4px"
            style="color: #f56c6c"
            v-if="qoqRateChangeInfo.change === 'up'"
          />
          <Icon
            icon="ep:caret-bottom"
            class="ml-8px mr-4px"
            style="color: #67c23a"
            v-if="qoqRateChangeInfo.change === 'down'"
          />
          <div class="ml-8px mr-4px" v-if="qoqRateChangeInfo.change === 'none'"></div>
          <div class="compare-num">
            {{
              qoqRateChangeInfo.change === 'up'
                ? '+'
                : qoqRateChangeInfo.change === 'down'
                ? '-'
                : ''
            }}{{ qoqRateChangeInfo.rate }}
          </div>
        </div>
      </div>
      <statisticsDialog ref="statisticsDialogRef" @change-time="statisticsDialogTime" />
    </div>
  </div>
</template>

<script setup name="DataSector" lang="ts">
import TimeSelect from '../Search/TimeSelect.vue'
import { generateDarkColors } from '@/utils/generateDarkColors'
import statisticsDialog from '../statisticsDialog.vue'

import auditApi from '../../../common/api'

const props = defineProps({
  initData: {
    type: Object,
    default: () => {}
  },
  initIndex: {
    type: Number,
    default: 0
  }
})

const data = ref()

const statisticsDialogRef = ref()

const styleData = reactive({
  width: '90%',
  height: '80%'
})

const TimeSelectRef = ref()

const echartHeight = computed(() => {
  return window.innerHeight / 6
})

const emits = defineEmits(['changeTime', 'initYear', 'initQoq'])

const times = ref()

const getTimeData = (_timePeriod) => {
  const arr = TimeSelectRef.value.timePeriod.map((el, index) => {
    return el
  })
  console.log('_timePeriod', arr)
  times.value = arr
  const obj = {
    code: props.initData.code,
    timeRanges: arr
  }
  emits('changeTime', obj)
}

const getData = async () => {
  await props.searchFn()
}

/**---------------------------------------------- 提示词 start ----------------------------------------------------------*/

import { useUnit } from '../../hooks/useUnit'

const { tipsMap, getUnitComputed, getTips } = useUnit()

// 根据tipsMap内容确定单位
const getUnit = getUnitComputed(computed(() => data.value?.code))

/**---------------------------------------------- 提示词 end ----------------------------------------------------------*/

/**---------------------------------------- 同比环比 start --------------------------------------------------*/
import { yearDateRangesMap } from '../../utils/yearDateRange'
import { qoqDateRangesMap } from '../../utils/qoqDateRange'

// 选中的同比
const selectYearProportion = ref({
  name: '年同比',
  type: 'year'
})

// 选中的环比
const selectQoqProportion = ref({
  name: '季环比',
  type: 'quarter'
})

// 同比选项
const yearSelectList = ref([
  {
    name: '年同比',
    type: 'year'
  },
  {
    name: '半年同比',
    type: 'half'
  },
  {
    name: '季同比',
    type: 'quarter'
  },
  {
    name: '月同比',
    type: 'month'
  }
])

const yearSelectMap = computed(() => {
  return yearSelectList.value.reduce((a, b) => {
    return {
      ...a,
      [b.type]: b
    }
  }, {})
})

// 环比选项
const qoqSelectList = ref([
  {
    name: '季环比',
    type: 'quarter'
  },
  {
    name: '月环比',
    type: 'month'
  },
  {
    name: '半年环比',
    type: 'half'
  }
])

const qoqSelectMap = computed(() => {
  return qoqSelectList.value.reduce((a, b) => {
    return {
      ...a,
      [b.type]: b
    }
  }, {})
})

// 计算比率的函数
import { calculateChange } from '../../utils'

import controller from '../../../common/controller'

import { getPackageAndCustomerServiceBusinessSumByCode } from '@/api/Audit'

import graphqlRequest from '@/utils/graphqlRequest'
import { getBusinessNumTimeStartEndMap } from '../../../common/sql'

// 同比选中的比率信息
const yearRateChangeInfo = ref({
  change: 'none',
  rate: '0'
})

const yearSelectLoading = ref(false)

// 同比选中
const yearSelectFn = async (_val, _isInit = false) => {
  selectYearProportion.value = _val
  console.log(
    `同比选中：${selectYearProportion.value.name}`,
    yearDateRangesMap[selectYearProportion.value.type]
  )
  try {
    if (
      // !getBusinessNumTimeStartEndMap()[data.value.code]
      !auditApi.getBusinessNumGeneral[data.value.code]
      // || getBusinessNumTimeStartEndMap()[data.value.code].isFake
    ) {
      return
    }
    // yearSelectLoading.value = true
    const selectedRage = yearDateRangesMap[selectYearProportion.value.type]
    console.log('selectedRage', selectedRage)
    // const pre_res = await graphqlRequest(
    //   getBusinessNumTimeStartEndMap(selectedRage[1])[data.value.code].sql
    // )
    const pre_res = await controller.getPackageAndCustomerServiceBusinessSumByCode({
      time: selectedRage[1],
      code: data.value.code
    })
    const pre_num = pre_res === null || pre_res === undefined ? undefined : Number(pre_res)
    // const next_res = await graphqlRequest(
    //   getBusinessNumTimeStartEndMap(selectedRage[0])[data.value.code].sql
    // )
    const next_res = await controller.getPackageAndCustomerServiceBusinessSumByCode({
      time: selectedRage[0],
      code: data.value.code
    })
    const next_num = next_res === null || next_res === undefined ? undefined : Number(next_res)
    yearRateChangeInfo.value = calculateChange(pre_num, next_num)
    if (_isInit) {
      emits('initYear', {
        code: data.value.code,
        pre_num,
        next_num
      })
    }
  } catch (e) {
    emits('initYear', {
      code: data.value.code,
      pre_num: 0,
      next_num: 0
    })
  } finally {
    yearSelectLoading.value = false
  }
}

// 环比选中的比率信息
const qoqRateChangeInfo = ref({
  change: 'none',
  rate: '0'
})

const qoqSelectLoading = ref(false)

// 环比选中
const qoqSelectFn = async (_val, _isInit = false) => {
  selectQoqProportion.value = _val
  console.log(
    `环比选中${selectQoqProportion.value.name}`,
    qoqDateRangesMap[selectQoqProportion.value.type]
  )
  try {
    if (
      // !getBusinessNumTimeStartEndMap()[data.value.code]
      !auditApi.getBusinessNumGeneral[data.value.code]
      //  || getBusinessNumTimeStartEndMap()[data.value.code].isFake
    ) {
      return
    }
    // qoqSelectLoading.value = true
    const selectedRage = qoqDateRangesMap[selectQoqProportion.value.type]
    // const pre_res = await graphqlRequest(
    //   getBusinessNumTimeStartEndMap(selectedRage[1])[data.value.code].sql
    // )
    const pre_res = await controller.getPackageAndCustomerServiceBusinessSumByCode({
      time: selectedRage[1],
      code: data.value.code
    })
    const pre_num = pre_res === null || pre_res === undefined ? undefined : Number(pre_res)
    // const next_res = await graphqlRequest(
    //   getBusinessNumTimeStartEndMap(selectedRage[0])[data.value.code].sql
    // )
    const next_res = await controller.getPackageAndCustomerServiceBusinessSumByCode({
      time: selectedRage[0],
      code: data.value.code
    })
    const next_num = next_res === null || next_res === undefined ? undefined : Number(next_res)
    qoqRateChangeInfo.value = calculateChange(pre_num, next_num)
    if (_isInit) {
      emits('initQoq', {
        code: data.value.code,
        pre_num,
        next_num
      })
    }
  } catch (e) {
    if (_isInit) {
      emits('initQoq', {
        code: data.value.code,
        pre_num: 0,
        next_num: 0
      })
    }
  } finally {
    qoqSelectLoading.value = false
  }
}

/**---------------------------------------- 同比环比 end --------------------------------------------------*/

// 是否整除4
const isDiv4 = computed(() => {
  return (props.initIndex + 1) % 4 === 0
})
const isDiv3 = computed(() => {
  return (props.initIndex + 1) % 3 === 0
})
const isDiv2 = computed(() => {
  return (props.initIndex + 1) % 2 === 0
})

import { bus_data, bus_color } from './fakeData'

const reset_color = computed(() => {
  return isDiv4.value
    ? bus_color.color_4
    : isDiv3.value
    ? bus_color.color_3
    : isDiv2.value
    ? bus_color.color_2
    : bus_color.color_1
})

const option = computed(() => {
  const fakeData = isDiv4.value
    ? bus_data.bus_4
    : isDiv3.value
    ? bus_data.bus_3
    : isDiv2.value
    ? bus_data.bus_2
    : bus_data.bus_1
  if (isDiv3.value) {
    return {
      // tooltip: {
      //   trigger: 'axis'
      // },
      xAxis: {
        type: 'category',

        boundaryGap: false,

        data: fakeData.map((el) => el.name),
        show: false,

        nameTextStyle: {
          lineheight: 20
        },
        axisTick: {
          alignWithLabel: true
        }
      },

      yAxis: {
        type: 'value',
        nameTextStyle: {
          lineheight: 20,
          overflow: 'break'
        },
        show: false
      },

      grid: {
        containLabel: false,
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%'
      },

      series: [
        {
          data: fakeData,

          type: 'line',

          smooth: false,

          areaStyle: {
            color: reset_color.value
          },
          lineStyle: {
            color: reset_color.value
          },

          itemStyle: {
            color: reset_color.value
          }
        }
      ]
    }
  } else if (isDiv2.value) {
    return {
      // tooltip: {
      //   trigger: 'axis'
      // },
      xAxis: {
        type: 'category',
        data: fakeData.map((el) => el.name),
        show: false,
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: {
        type: 'value',
        show: false
      },
      grid: {
        containLabel: false,
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%'
      },
      series: [
        {
          data: fakeData,
          type: 'bar',
          itemStyle: {
            color: reset_color.value
          }
        }
      ]
    }
  } else {
    return {
      // tooltip: {
      //   trigger: 'axis'
      // },
      xAxis: {
        type: 'category',

        boundaryGap: false,

        data: fakeData.map((el) => el.name),
        show: false,

        nameTextStyle: {
          lineheight: 20
        },
        axisTick: {
          alignWithLabel: true
        }
      },

      yAxis: {
        type: 'value',
        nameTextStyle: {
          lineheight: 20,
          overflow: 'break'
        },
        show: false
      },

      grid: {
        containLabel: false,
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%'
      },

      series: [
        {
          data: fakeData,

          type: 'line',

          smooth: true,

          areaStyle: {
            color: reset_color.value
          },
          lineStyle: {
            color: reset_color.value
          },

          itemStyle: {
            color: reset_color.value
          }
        }
      ]
    }
  }
})

import { colors } from '../colors'
import { deepClone } from '@/utils/deep'

const getDataFn = () => {}

const checkDetail = () => {
  const obj = deepClone(option.value)
  obj.yAxis.show = true
  obj.xAxis.show = true
  obj.grid.containLabel = true
  obj.series[0].label = {
    show: true,
    position: 'top'
  }
  statisticsDialogRef.value.open(obj, data.value.name, {
    type: 'timeRange',
    times: times.value,
    code: data.value.code,
    dataType: 'package' // 客户总量 customerSum，套餐业务 package，套餐业务总数 packageSum
  })
}

watch(
  () => props.initData,
  (_val) => {
    data.value = _val

    // 有firstShowCompareInfo字段说明是首屏，需要关闭loading
    if (_val.hasOwnProperty('firstShowCompareInfo')) {
      try {
        if (_val.firstShowCompareInfo) {
          // 设置同比环比
          try {
            yearRateChangeInfo.value = calculateChange(
              _val.firstShowCompareInfo.year_pre,
              _val.firstShowCompareInfo.year_next
            )
          } catch (e) {
            yearRateChangeInfo.value = {
              change: 'none',
              rate: '数据有误'
            }
          } finally {
            yearSelectLoading.value = false
          }

          try {
            qoqRateChangeInfo.value = calculateChange(
              _val.firstShowCompareInfo.qoq_pre,
              _val.firstShowCompareInfo.qoq_next
            )
          } catch (e) {
            qoqRateChangeInfo.value = {
              change: 'none',
              rate: '数据有误'
            }
          } finally {
            qoqSelectLoading.value = false
          }
        }
      } finally {
        nextTick(() => {
          yearSelectLoading.value = false
          qoqSelectLoading.value = false
        })
      }
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const showNoDataTip = () => {
  ElMessage.error('暂无数据')
}

const initSelectFn = () => {
  nextTick(() => {
    yearSelectFn({
      name: '年同比',
      type: 'year'
    })
    qoqSelectFn({
      name: '季环比',
      type: 'quarter'
    })
  })
}

const statisticsDialogTime = (_time) => {
  TimeSelectRef.value.timePeriod = _time
  // getTimeData()
}

watch(
  () => data.value,
  (_val) => {
    if (statisticsDialogRef.value.show) {
      checkDetail()
      // statisticsDialogRef.value.loading = false
    }
  },
  {
    deep: true
  }
)

const statisticsDialogIsShow = computed(() => {
  return statisticsDialogRef.value.show || false
})

const setInitCompare = (_map) => {
  console.log('_map--------------------------->', _map)
  console.log('data.value--------------------->', data.value)
  try {
    const code = data.value.code
    if (_map[code]) {
      yearRateChangeInfo.value = calculateChange(_map[code].year_pre, _map[code].year_next)
    }
  } finally {
    yearSelectLoading.value = false
  }

  try {
    const code = data.value.code
    if (_map[code]) {
      qoqRateChangeInfo.value = calculateChange(_map[code].qoq_pre, _map[code].qoq_next)
    }
  } finally {
    qoqSelectLoading.value = false
  }
}

onMounted(() => {
  getTimeData()
  // yearSelectLoading.value = true
  // qoqSelectLoading.value = true
  yearSelectFn(
    {
      name: '年同比',
      type: 'year'
    },
    true
  )
  qoqSelectFn(
    {
      name: '季环比',
      type: 'quarter'
    },
    true
  )
})

defineExpose({
  initSelectFn,
  setInitCompare
})
</script>

<style scoped lang="less">
.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-center {
  justify-content: center;
  align-items: center;
}
</style>
