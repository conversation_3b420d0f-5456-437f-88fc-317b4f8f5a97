<!--
 * @Author: hao-jie.chen <EMAIL>
 * @Date: 2025-01-07 15:40:50
 * @LastEditors: hao-jie.chen <EMAIL>
 * @LastEditTime: 2025-01-10 14:59:11
 * @Description:  时间段选择器 默认近十二个月
-->
<template>
  <div class="">
    <!-- 年份 -->

    <el-date-picker
      class="text-color-white !w-[180px]"
      v-model="timePeriod"
      @change="changeYear"
      type="monthrange"
      :format="dateFormatType"
      :value-format="dateFormatType"
      unlink-panels
      range-separator="To"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      :shortcuts="shortcuts"
      :teleported="false"
      size="small"
    />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'TimeSelect'
})

import dayjs from 'dayjs'

// 时间快捷选择
const shortcuts = [
  {
    text: '今年以来',
    value: () => {
      const end = new Date()
      const start = new Date(new Date().getFullYear(), 0)
      return [start, end]
    }
  },
  {
    text: '近12个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 12)
      return [start, end]
    }
  },
  {
    text: '近6个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 6)
      return [start, end]
    }
  },
  {
    text: '这个月',
    value: [new Date(), new Date()]
  }
]

import { isEmpty } from 'lodash-es'

const props = defineProps({})
const dateFormatType = ref('YYYY-MM')

const emit = defineEmits(['getData'])

/**时间段,默认今年以来 */
const timePeriod = ref<string[]>([
  dayjs(new Date(new Date().getFullYear(), 0)).format('YYYY-MM'),
  dayjs(new Date()).format('YYYY-MM')
])

const changeYear = async (val) => {
  console.log(val)

  if (isEmpty(val)) {
    timePeriod.value = [dayjs().subtract(12, 'month').format('YYYY-MM'), dayjs().format('YYYY-MM')]
  }
  await emit('getData')
}

defineExpose({
  timePeriod
})
</script>

<style lang="less" scoped></style>
