<template>
  <div
    class="distribution-sector-chart-1 bg-white p-16px"
    style="height: 49%; border-radius: 4px; box-shadow: var(--el-box-shadow-light)"
    v-loading="loading && !amplifyDialogIsShow"
  >
    <div style="height: 100%">
      <div class="flex flex-between">
        <p>{{ title }}</p>
        <TimeSelect ref="TimeSelectRef" @get-data="getTime" />
      </div>
      <div class="chart-warp"> </div>
      <echart
        ref="echartRef"
        :options="option"
        :width="width"
        :height="height"
        @click="checkDetail"
      />
      <div class="flex" style="justify-content: space-around; flex-direction: row-reverse">
        <div
          class="quantity-warp w-3/8 b-32px mt-8px"
          v-for="(item, index) in data"
          :key="index"
          v-show="data.length <= 3 || index + 1 > data.length - 3"
        >
          <div class="flex" style="justify-content: center; align-items: center">
            <div
              class="round w-10px h-10px mr-8px"
              :style="{ borderRadius: '50%', background: item.itemStyle?.color }"
            >
            </div>
            <div class="title" style="font-size: 16px; max-width: 140px">{{ item.name }}</div>
          </div>
          <div class="values" style="text-align: center; font-size: 20px; color: grey">
            {{ item.value === undefined ? '--' : item.value }}
            <span class="text-xs" v-if="item.value !== undefined">{{ getUnit(item.code) }}</span>
          </div>
        </div>
      </div>
      <amplifyDialog ref="amplifyDialogRef" @change-time="amplifyChangeTime" />
    </div>
  </div>
</template>

<script setup name="DataSector" lang="ts">
import TimeSelect from '../Search/TimeSelect.vue'
import { generateDarkColors } from '@/utils/generateDarkColors'
import amplifyDialog from '../amplifyDialog.vue'
import { getPackageAndCustomerService } from '@/api/Audit'
import { useUnit } from '../../hooks/useUnit'

const { getUnit } = useUnit()

import controller from '../../../common/controller'

const props = defineProps({
  tenantList: {
    type: Array,
    default: () => []
  }
})

const amplifyDialogRef = ref()

const title = ref('套餐业务数据分布')

const width = ref('100%')
const height = ref('70%')

const TimeSelectRef = ref()

const time = ref([])

const times = ref()

const getTime = () => {
  time.value = TimeSelectRef.value.timePeriod
  times.value = TimeSelectRef.value.timePeriod
  getDataFn()
}

const option = computed(() => {
  const maxNum = data.value && data.value.length > 0 ? data.value[data.value.length - 1].value : 0 //计算最大值

  return {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      max: Math.floor(maxNum * 1.1)
    },
    yAxis: [
      {
        type: 'category',
        data: data.value.map((el) => el.name),
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    series: [
      {
        type: 'bar',
        barWidth: '60%',
        data: data.value
      }
    ],
    dataZoom:
      data.value.length > 10
        ? [
            {
              type: 'slider',
              orient: 'vertical',
              yAxisIndex: 0,
              start: 1000,
              end: 990,
              minValueSpan: 7,
              width: 10,
              showDetail: false
            },
            {
              type: 'inside',
              orient: 'vertical'
            }
          ]
        : []
  }
})
import { deepClone } from '@/utils/deep'
// 查看大图
const checkDetail = () => {
  const obj = deepClone(option.value)
  if (data.value.length > 8) {
    obj.dataZoom[0].width = 20
  }
  obj.series[0].label = {
    show: true,
    position: 'right'
  }
  amplifyDialogRef.value.open(obj, title.value, {
    type: 'timeRange',
    times: times.value
  })
}

const data = ref([])

import { colors } from '../colors'

import { getBusinessNumTimePickerMap } from '../../../common/sql'
import graphqlRequest from '@/utils/graphqlRequest'

const loading = ref(false)

const getDataFn = async (_list?) => {
  try {
    loading.value = true
    // const tenantList = deepClone(props.tenantList)
    // if (tenantList.length > 0) {
    //   const tenantResArr: any = [] //通过地址指针的方式赋值
    //   const businessRequestList: any = []
    //   for (let i = 0; i < tenantList.length; i++) {
    //     if (getBusinessNumTimePickerMap(time.value)[tenantList[i].code]) {
    //       // if (!getBusinessNumTimePickerMap(time.value)[tenantList[i].code].isFake) {
    //       // 检测是否有isFake，isFake为true一律返回0
    //       businessRequestList.push(
    //         graphqlRequest(getBusinessNumTimePickerMap(time.value)[tenantList[i].code].sql)
    //       )
    //       // }

    //       tenantResArr.push(tenantList[i])
    //     }
    //   }
    //   // 业务数据
    //   const businessAllRes = await Promise.allSettled(businessRequestList)

    //   for (let index = 0; index < tenantResArr.length; index++) {
    //     // if (getBusinessNumTimePickerMap(time.value)[tenantResArr[index].code].isFake) {
    //     //   //检测是否有isFake，isFake为true一律返回0
    //     //   tenantResArr[index].value = 0
    //     // } else

    //     if (businessAllRes[index] && businessAllRes[index].status == 'fulfilled') {
    //       const busRes = businessAllRes[index].value
    //       tenantResArr[index].value = Number(busRes.data[0]['count'])
    //     }
    //   }
    // } else {
    //   return
    // }

    let list: any = []
    if (_list) {
      list = deepClone(_list)
    } else {
      list = await controller.getPackageAndCustomerService({
        time: times.value
      })
    }

    // data.value = tenantList
    data.value = list.map((el) => {
      el.value = el.business_num
      return el
    })

    data.value = data.value.sort((a, b) => {
      let aNum = 0
      let bNum = 0
      aNum = a.value
      bNum = b.value
      if (a.value === undefined) {
        aNum = -1
      }
      if (b.value === undefined) {
        bNum = -1
      }

      return aNum - bNum
    })

    if (data.value.length <= colors.length) {
      data.value.map((el, index) => {
        el.itemStyle = {
          color: colors[index]
        }
      })
    } else {
      const _colors = generateDarkColors(data.value.length, colors)
      data.value.map((el, index) => {
        el.itemStyle = {
          color: _colors[data.value.length - index - 1]
          // color: _colors[index]
        }
      })
    }
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // nextTick(() => {
  //   getTime()
  // })
})

// watch(
//   () => props.tenantList,
//   () => {
//     nextTick(() => {
//       getTime()
//     })
//   },
//   {
//     deep: true
//   }
// )

const amplifyChangeTime = (_time) => {
  TimeSelectRef.value.timePeriod = _time
  getTime()
}

watch(
  () => data.value,
  (_newVal) => {
    if (amplifyDialogRef.value.show) {
      checkDetail()
      amplifyDialogRef.value.loading = false
    }
  },
  {
    deep: true
  }
)

const amplifyDialogIsShow = computed(() => {
  return amplifyDialogRef.value.show || false
})

defineExpose({
  loading,
  getDataFn
})
</script>

<style scoped lang="less">
.flex-between {
  justify-content: space-between;
}
</style>
