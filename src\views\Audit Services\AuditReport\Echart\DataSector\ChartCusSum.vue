<template>
  <div
    class="data-sector-chart-1 w-full mr-16px"
    v-loading="customerLoading && !statisticsDialogIsShow"
  >
    <div
      class="distribution-sector-chart-1 bg-white p-16px pb-8px"
      style="border-radius: 4px; box-shadow: var(--el-box-shadow-light)"
    >
      <div class="header flex flex-between">
        <div class="header-left flex" style="align-items: center">
          <div class="title mr-8px" style="font-size: 14px">{{ title }}</div>
          <YearSelect ref="YearSelectRef" @get-data="getTimeData" />
        </div>
        <div class="header-right">
          <el-tooltip :content="''">
            <Icon icon="ant-design:info-circle-outlined" />
          </el-tooltip>
        </div>
      </div>
      <div class="sum-num mt-8px pl-8px" style="font-size: 26px">
        {{ customerNum === undefined ? '--' : customerNum }}
        <span class="text-xl">个</span>
      </div>
      <el-tooltip
        :content="customerNum === undefined ? '暂无数据' : '点击查看详情'"
        placement="top"
        :offset="-8"
      >
        <div class="w-100% flex" style="justify-content: center">
          <echart :options="option" :width="'100%'" :height="'70%'" @click="checkDetail" />
        </div>
      </el-tooltip>

      <div class="ratio-warp flex flex-center mt-8px">
        <div
          class="on-year flex flex-center p-4px w-1/2"
          style="margin: 0 16px"
          v-loading="yearSelectLoading"
        >
          <el-dropdown @command="yearSelectFn">
            <el-button link style="color: var(--el-color-primary)">{{
              selectYearProportion?.name
            }}</el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="(item, index) in yearSelectList"
                  :key="index"
                  :command="item"
                  >{{ item.name }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <Icon
            icon="ep:caret-top"
            class="ml-8px mr-4px"
            style="color: #f56c6c"
            v-if="yearRateChangeInfo.change === 'up'"
          />
          <Icon
            icon="ep:caret-bottom"
            class="ml-8px mr-4px"
            style="color: #67c23a"
            v-if="yearRateChangeInfo.change === 'down'"
          />
          <div class="ml-8px mr-4px" v-if="yearRateChangeInfo.change === 'none'"></div>
          <div class="compare-num">
            {{
              yearRateChangeInfo.change === 'up'
                ? '+'
                : yearRateChangeInfo.change === 'down'
                ? '-'
                : ''
            }}{{ yearRateChangeInfo.rate }}
          </div>
        </div>
        <div
          class="on-year flex flex-center p-4px w-1/2"
          style="margin: 0 16px"
          v-loading="qoqSelectLoading"
        >
          <el-dropdown @command="qoqSelectFn">
            <el-button link style="color: var(--el-color-primary)">{{
              selectQoqProportion?.name
            }}</el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="(item, index) in qoqSelectList"
                  :key="index"
                  :command="item"
                  >{{ item.name }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <Icon
            icon="ep:caret-top"
            class="ml-8px mr-4px"
            style="color: #f56c6c"
            v-if="qoqRateChangeInfo.change === 'up'"
          />
          <Icon
            icon="ep:caret-bottom"
            class="ml-8px mr-4px"
            style="color: #67c23a"
            v-if="qoqRateChangeInfo.change === 'down'"
          />
          <div class="ml-8px mr-4px" v-if="qoqRateChangeInfo.change === 'none'"></div>
          <div class="compare-num">
            {{
              qoqRateChangeInfo.change === 'up'
                ? '+'
                : qoqRateChangeInfo.change === 'down'
                ? '-'
                : ''
            }}{{ qoqRateChangeInfo.rate }}
          </div>
        </div>
      </div>
      <statisticsDialog ref="statisticsDialogRef" @change-time="statisticsChangeTime" />
    </div>
  </div>
</template>

<script setup name="DataSector" lang="ts">
import YearSelect from '../Search/YearSelect.vue'
import { generateDarkColors } from '@/utils/generateDarkColors'
import statisticsDialog from '../statisticsDialog.vue'

const props = defineProps({
  searchFn: {
    type: Function,
    default: () => {}
  }
})

const title = ref('订阅客户总量')

const statisticsDialogRef = ref()

const styleData = reactive({
  width: '90%',
  height: '80%'
})

const YearSelectRef = ref()

const echartHeight = computed(() => {
  return window.innerHeight / 6
})

const emits = defineEmits(['changeYear'])

const times = ref()

const customerNum: Ref<Number | undefined> = ref(0)

const customerLoading = ref(false)

// 获取订阅客户总量
const getCustomerSumData = async (_year) => {
  try {
    customerLoading.value = true
    const customer_num_sum_res = await controller.getPackageAndCustomerServiceCustomerSum({
      time: _year
    })
    console.log('customer_num_sum_res-------------------------', customer_num_sum_res)
    customerNum.value = Number(customer_num_sum_res.data.count)
  } finally {
    customerLoading.value = false
  }
}

const getTimeData = (_year) => {
  times.value = YearSelectRef.value.year
  getCustomerSumData(times.value)
}

const getData = async () => {
  await props.searchFn()
}

const allTenantList = ref([])

/**---------------------------------------------- 提示词 start ----------------------------------------------------------*/

import { useUnit } from '../../hooks/useUnit'

const { tipsMap, getTips } = useUnit()

/**---------------------------------------------- 提示词 end ----------------------------------------------------------*/

/**---------------------------------------- 同比环比 start --------------------------------------------------*/
// 计算比率的函数
import { calculateChange } from '../../utils'

import graphqlRequest from '@/utils/graphqlRequest'
import { getBusinessNumTimeStartEndMap } from '../../../common/sql'

import { yearDateRangesMap } from '../../utils/yearDateRange'
import { qoqDateRangesMap } from '../../utils/qoqDateRange'

import { addHours, getLastDayOfMounth } from '@/utils/formatTime'

import { getSumTimeSql } from '../../../common/sql'

import controller from '../../../common/controller'

import {
  getPackageAndCustomerServiceCustomerSum,
  getPackageAndCustomerServiceBusinessSum
} from '@/api/Audit'

// 订阅客户总量
const getCustomerSumByStartEnd = (_time) => {
  let timeSql = ''

  if (_time) {
    const startTime = `${_time?.start} 00:00:00`
    const endTime = `${_time?.end} 23:59:59`

    const getStartTimestamp = addHours(startTime)
    const getEndTimestamp = addHours(endTime)
    timeSql = `AND jt.date_val > ${getStartTimestamp} AND jt.date_val < ${getEndTimestamp}`
  } else {
    timeSql = getSumTimeSql()
  }

  return `"SELECT count( DISTINCT bus.tenant_id ) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN system_tenant_package_subscription bus ON bus.package_code = m.entity_id AND bus.tenant_id = m.entity_id_2nd WHERE m.entity = 'system_tenant_package_subscription' ${timeSql} AND bus.id IS NOT NULL AND bus.status = 0 AND bus.deleted = 0"`

  // return `"SELECT count( DISTINCT bus.tenant_id ) AS count FROM manifest m JOIN JSON_TABLE ( JSON_EXTRACT( m.manifest, '$.created' ), '$[*]' COLUMNS ( date_val  DECIMAL(16, 1) PATH '$' ) ) jt LEFT JOIN system_tenant_package_subscription bus ON bus.package_code = m.entity_id AND bus.tenant_id = m.entity_id_2nd WHERE m.entity = 'system_tenant_package_subscription' AND jt.date_val > ${getStartTimestamp} AND jt.date_val < ${getEndTimestamp} AND bus.id IS NOT NULL AND bus.STATUS = 0 AND bus.deleted = 0"`
}

// 选中的同比
const selectYearProportion = ref({
  name: '年同比',
  type: 'year'
})

// 同比选中的比率信息
const yearRateChangeInfo = ref({
  change: 'none',
  rate: '0'
})

// 选中的环比
const selectQoqProportion = ref({
  name: '季环比',
  type: 'quarter'
})

// 环比选中的比率信息
const qoqRateChangeInfo = ref({
  change: 'none',
  rate: '0'
})

// 同比选项
const yearSelectList = ref([
  {
    name: '年同比',
    type: 'year'
  },
  {
    name: '半年同比',
    type: 'half'
  },
  {
    name: '季同比',
    type: 'quarter'
  },
  {
    name: '月同比',
    type: 'month'
  }
])

const yearSelectMap = computed(() => {
  return yearSelectList.value.reduce((a, b) => {
    return {
      ...a,
      [b.type]: b
    }
  }, {})
})

// 环比选项
const qoqSelectList = ref([
  {
    name: '季环比',
    type: 'quarter'
  },
  {
    name: '月环比',
    type: 'month'
  },
  {
    name: '半年环比',
    type: 'half'
  }
])

const qoqSelectMap = computed(() => {
  return qoqSelectList.value.reduce((a, b) => {
    return {
      ...a,
      [b.type]: b
    }
  }, {})
})

const yearSelectLoading = ref(false)

// // 获取订阅客户总量
// const getCustomerSumData = async () => {
//   try {
//     customerLoading.value = true
//     const customer_num_sum_res = await graphqlRequest(CUSTOM_NUM_SUM_SQL.value)
//     customerNum.value = Number(customer_num_sum_res.data[0]['count( DISTINCT bus.tenant_id )'])
//   } finally {
//     customerLoading.value = false
//   }
// }

// // 获取业务数据总量
// const getBusinessSumData = async (_timeObj, tenantList) => {
//   try {
//     let businessNum = 0
//     if (tenantList.length > 0) {
//       const tenantResArr = [] //通过地址指针的方式赋值
//       const businessRequestList = []
//       for (let i = 0; i < tenantList.length; i++) {
//         if (getBusinessNumTimeStartEndMap(_timeObj)[tenantList[i].code]) {
//           // if (!getBusinessNumTimeStartEndMap(_timeObj)[tenantList[i].code].isFake) {
//           //检测是否有isFake，isFake为true一律返回0
//           businessRequestList.push(
//             graphqlRequest(getBusinessNumTimeStartEndMap(_timeObj)[tenantList[i].code].sql)
//           )
//           tenantResArr.push(tenantList[i])
//           // }
//         }
//       }
//       // 业务数据
//       const businessAllRes = await Promise.allSettled(businessRequestList)

//       for (let index = 0; index < tenantResArr.length; index++) {
//         if (businessAllRes[index] && businessAllRes[index].status == 'fulfilled') {
//           const busRes = businessAllRes[index].value
//           businessNum += Number(busRes.data[0]['count'])
//         }
//       }
//     }
//     return businessNum
//   } finally {
//   }
// }

// 选择同比
const yearSelectFn = async (_val) => {
  selectYearProportion.value = _val
  console.log(
    `同比选中：${selectYearProportion.value.name}`,
    yearDateRangesMap[selectYearProportion.value.type]
  )
  try {
    // yearSelectLoading.value = true
    const selectedRage = yearDateRangesMap[selectYearProportion.value.type]
    //客户总量
    const pre_res = await controller.getPackageAndCustomerServiceCustomerSum({
      time: selectedRage[1]
    })
    const pre_num = Number(pre_res.data.count)
    const next_res = await controller.getPackageAndCustomerServiceCustomerSum({
      time: selectedRage[0]
    })
    const next_num = Number(next_res.data.count)
    yearRateChangeInfo.value = calculateChange(pre_num, next_num)
  } finally {
    yearSelectLoading.value = false
  }
}

const qoqSelectLoading = ref(false)

// 选择环比
const qoqSelectFn = async (_val) => {
  selectQoqProportion.value = _val
  console.log(
    `环比选中8949${selectQoqProportion.value.name}`,
    qoqDateRangesMap[selectQoqProportion.value.type]
  )
  try {
    // qoqSelectLoading.value = true
    const selectedRage = qoqDateRangesMap[selectQoqProportion.value.type]
    //客户总量
    const pre_res = await controller.getPackageAndCustomerServiceCustomerSum({
      time: selectedRage[1]
    })
    const pre_num = Number(pre_res.data.count)
    const next_res = await controller.getPackageAndCustomerServiceCustomerSum({
      time: selectedRage[0]
    })
    const next_num = Number(next_res.data.count)
    qoqRateChangeInfo.value = calculateChange(pre_num, next_num)
  } finally {
    qoqSelectLoading.value = false
  }
}

/**---------------------------------------- 同比环比 end --------------------------------------------------*/

const option = computed(() => {
  return {
    // tooltip: {
    //   trigger: 'axis'
    // },
    xAxis: {
      type: 'category',

      boundaryGap: false,

      data: data.value?.list.map((el) => el.name),
      show: false,

      nameTextStyle: {
        lineheight: 20
      },
      axisTick: {
        alignWithLabel: true
      }
    },

    yAxis: {
      type: 'value',
      nameTextStyle: {
        lineheight: 20
        // overflow: 'break'
      },
      show: false
      // nameTextStyle: {
      //   height: 60
      // }
    },

    grid: {
      containLabel: false,
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%'
    },

    series: [
      {
        data: data.value?.list.map((el) => el.value),

        type: 'line',

        smooth: true,

        areaStyle: {
          color: 'rgb(163, 223, 131)'
        },

        lineStyle: {
          color: 'rgb(163, 223, 131)'
        },

        itemStyle: {
          color: 'rgb(163, 223, 131)'
        }
      }
    ]
  }
})

import { colors } from '../colors'

const data = ref()

const getDataFn = () => {
  data.value = {
    sum: 1230,
    list: []
  }
}

import { deepClone } from '@/utils/deep'

const checkDetail = () => {
  const obj = deepClone(option.value)
  obj.yAxis.show = true
  obj.xAxis.show = true
  obj.grid.containLabel = true
  obj.series[0].label = {
    show: true,
    position: 'top'
  }
  statisticsDialogRef.value.open(obj, title.value, {
    type: 'year',
    times: times.value,
    dataType: 'customerSum' // 客户总量 customerSum，套餐业务 package，套餐业务总数 packageSum
  })
}

import { sum_1, sum_2 } from './fakeData'

onMounted(() => {
  customerLoading.value = true
  getDataFn()
  getData()
})

/**
 * 设置首屏总数
 * @param _list 从上层传过来的，带有各个套餐业务数据的列表，但因为和其他部分相对独立，所以只要正常请求即可
 * */
const setInitData = (_list) => {
  allTenantList.value = deepClone(_list)
  nextTick(() => {
    getTimeData(YearSelectRef.value.year)
    yearSelectFn({
      name: '年同比',
      type: 'year'
    })
    qoqSelectFn({
      name: '季环比',
      type: 'quarter'
    })
    data.value.list = sum_1
  })
}

const statisticsChangeTime = (_time) => {
  YearSelectRef.value.year = _time
  // getTimeData()
}

const statisticsDialogIsShow = computed(() => {
  return statisticsDialogRef.value.show || false
})

defineExpose({
  setInitData
})
</script>

<style scoped lang="less">
.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-center {
  justify-content: center;
  align-items: center;
}
</style>
