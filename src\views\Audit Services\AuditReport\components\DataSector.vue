<!-- 数据板块 -->
<template>
  <div class="data-sector h-full flex" style="flex-direction: column">
    <!-- 总体情况 -->
    <div
      class="overall-situation mb-8px"
      style="background: ghostwhite; padding: 8px 16px"
      ref="sumRef"
    >
      <h3 class="mb-8px" style="font-size: 18px">总体情况</h3>
      <el-row :gutter="16" class="w-full">
        <el-col :span="12">
          <ChartCusSum
            :search-fn="customerData[0].searchFn"
            @change-year="changeYear"
            ref="dataSectorCusSumChartRef"
          />
        </el-col>
        <el-col :span="12">
          <ChartBusSum
            :search-fn="customerData[1].searchFn"
            @change-year="changeYear"
            ref="dataSectorBusSumChartRef"
          />
        </el-col>
      </el-row>
    </div>
    <div
      class="business-situation"
      style="background: ghostwhite; flex: 1; padding: 8px 16px"
      ref="busSitRef"
      v-loading="businessLoading"
    >
      <h3 class="mb-8px" style="font-size: 18px">套餐业务数据情况</h3>
      <el-scrollbar
        :style="{
          width: '100%',
          height: height
        }"
        ref="scrollRef"
        :always="true"
        @scroll="handleScroll"
        class="business-situation-scroll"
        ><div>
          <el-row :gutter="16" class="w-full" style="max-height: 160px">
            <el-col v-for="(item, index) in businessData" :key="index" :span="8">
              <Chart2
                :init-data="item"
                :init-index="index"
                @change-time="(_val) => changeTime(_val, item)"
                ref="busChartRef"
                @init-year="busChartYear"
                @init-qoq="busChartQoq"
              />
            </el-col>
          </el-row>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup name="DataSector" lang="ts">
/**----------------------------------------------------- sql语句 start --------------------------------------------------------------------**/

/**----------------------------------------------------- sql语句 end --------------------------------------------------------------------**/

import ChartCusSum from '../Echart/DataSector/ChartCusSum.vue'
import ChartBusSum from '../Echart/DataSector/ChartBusSum.vue'
import Chart2 from '../Echart/DataSector/Chart2.vue'

const allPackageServiceList = ref([])

const customerData = computed(() => {
  return [
    {
      searchFn: () => {}
    },
    {
      searchFn: () => {}
    }
  ]
})

// 客户总量的年份
const custom_num_year = ref('')

// 更改年限
const changeYear = (_obj) => {}

const businessData = ref([])

/**----------------------------------------- 处理数据 start -----------------------------------------------------------**/

import controller from '../../common/controller'

const dataSectorCusSumChartRef = ref()
const dataSectorBusSumChartRef = ref()

/**----------------------------------------- 处理数据 end -----------------------------------------------------------**/

/**------------------------------------------- 套餐业务数据情况 start ------------------------------------------------------------*/

import { getBusinessNumTimePickerMap } from '../../common/sql'
import { deepClone } from '@/utils/deep'

const businessLoading = ref(false)

const groupArr = (_arr) => {
  const res: any = []
  for (let i = 0; i < _arr.length; i += 9) {
    res.push(_arr.slice(i, i + 9))
  }
  return res
}

const busChartRef = ref()

const pageSize = ref(9)

const limitIndex = ref(0)

// 获取所有的套餐(分页)
const getBusinessData = async () => {
  try {
    businessLoading.value = true

    const packageServiceList = deepClone(perSortList.value)

    const startIndex = limitIndex.value * pageSize.value

    const endIndex = limitIndex.value * pageSize.value + 9

    // const listByPageSize = packageServiceList.slice(startIndex, endIndex)
    const listByPageSize = packageServiceList

    businessData.value = [
      ...businessData.value,
      ...listByPageSize.map((el) => {
        el.list = []
        el.loading = false
        return el
      })
    ]
    if (businessData.value.length === 0 || listByPageSize.length < 9) {
      isEnd.value = true
    }
  } finally {
    businessLoading.value = false
  }
}

const scrollRef = ref()

const isEnd = ref(false)

const handleScroll = () => {
  // if (scrollRef.value && !isEnd.value) {
  //   const warp = scrollRef.value.$el.querySelector('.el-scrollbar__wrap')
  //   if (warp) {
  //     const { scrollTop, clientHeight, scrollHeight } = warp
  //     if (scrollTop + clientHeight >= scrollHeight) {
  //       limitIndex.value = limitIndex.value + 1
  //       getBusinessData()
  //       if (businessData.value.length >= allPackageServiceList.value.length) {
  //         isEnd.value = true
  //       }
  //     }
  //   }
  // }
}

const changeTime = async (_val, _item) => {
  try {
    // if (!getBusinessNumTimePickerMap(_val.timeRanges)[_val.code].isFake) {
    //检测是否有isFake，isFake为true一律返回0
    _item.loading = true
    const res = await controller.getPackageAndCustomerServiceBusinessSumByCode({
      time: _val.timeRanges,
      code: _val.code
    })

    _item.sum = res === null || res === undefined ? undefined : Number(res)
    // } else {
    //   _item.sum = 0
    // }
  } finally {
    _item.loading = false
  }
}

/**------------------------------------------- 套餐业务数据情况 end ------------------------------------------------------------*/

const height = ref(0)

const busSitRef = ref()

const sumRef = ref()

// 设置套餐数据情况的高度
const getBusinessHeight = () => {
  nextTick(() => {
    // const el = busSitRef.value.clientHeight
    const sumElHeight = sumRef.value.clientHeight
    const el = document.documentElement.clientHeight
    console.log('el', el)
    console.log('sumElHeight', sumElHeight)
    height.value = `${el - 180 - sumElHeight}px`
  })
}

import { formatDate } from '@/utils/formatTime'

const perSortList = ref([])

// 根据时间段对所有的租户列表进行排序
const sortAllPackageServiceList = async (_list?) => {
  const end = new Date()
  const start = new Date()
  start.setMonth(start.getMonth() - 12)
  const time = [formatDate(start, 'YYYY-MM'), formatDate(end, 'YYYY-MM')]
  try {
    businessLoading.value = true
    let res: any = []
    if (_list) {
      res = deepClone(_list)
    } else {
      res = await controller.getPackageAndCustomerService({
        time
      })
    }

    perSortList.value = res.sort((a, b) => {
      if (
        (a.business_num === undefined || a.business_num === null) &&
        (b.business_num === undefined || b.business_num === null)
      ) {
        return 0
      }
      if (a.business_num === undefined || a.business_num === null) {
        return 1
      }
      if (b.business_num === undefined || b.business_num === null) {
        return -1
      }
      if (a.business_num == b.business_num) {
        return 0
      }

      return b.business_num - a.business_num
    })
    // perSortList.value = res
  } finally {
    businessLoading.value = false
  }
}

// 设置首屏业务数据总量
const getFirstShowStepOne = async (_list) => {
  // 从上层拿到包含客户订阅数和业务数的列表，传递给下层组件
  allPackageServiceList.value = _list
  dataSectorBusSumChartRef.value.setInitData(allPackageServiceList.value)
  dataSectorCusSumChartRef.value.setInitData(allPackageServiceList.value)
  // 设置套餐数据情况的高度
  getBusinessHeight()
  // 对套餐业务数据情况进行排序
  await sortAllPackageServiceList(_list)
  // 设置所有的套餐(套餐业务数据情况分页)
  getBusinessData()
}

// 设置首屏业务数据和总量的同比环比
const getFirstShowStepTwo = (_list) => {
  // 设置套餐业务数据总量的同比环比
  dataSectorBusSumChartRef.value.setInitCompare(deepClone(_list))
  // 创造一个map，减少循环请求
  const map = deepClone(_list).reduce((a, b) => {
    return {
      ...a,
      [b.code]: b.firstShowCompareInfo
    }
  }, {})
  // 设置首屏业务数据的同比环比
  businessData.value.forEach((el) => {
    if (map[el.code]) {
      el.firstShowCompareInfo = map[el.code]
    } else {
      // 没有的也设置，这是为了关闭loading
      el.firstShowCompareInfo = false
    }
  })
  perSortList.value.forEach((el) => {
    if (map[el.code]) {
      el.firstShowCompareInfo = map[el.code]
    } else {
      el.firstShowCompareInfo = false
    }
  })
}

const busChartYear = (_obj) => {
  dataSectorBusSumChartRef.value.setBusChartYear(_obj)
}

const busChartQoq = (_obj) => {
  dataSectorBusSumChartRef.value.setBusChartQoq(_obj)
}

onMounted(async () => {
  businessLoading.value = true
  // await getAllTenantList()
  // // dataSectorSumChartRef.value.forEach((el) => {
  // //   el.setAllTenantList(allPackageServiceList.value)
  // // })
  // dataSectorBusSumChartRef.value.setAllTenantList(allPackageServiceList.value)
  // dataSectorCusSumChartRef.value.setAllTenantList(allPackageServiceList.value)
  // // getBusinessSumData()
  // getBusinessHeight()
  // await sortAllPackageServiceList()
  // getBusinessData()
})

window.onresize = () => {
  getBusinessHeight()
}

defineExpose({
  getFirstShowStepOne,
  getFirstShowStepTwo
})
</script>

<style scoped lang="less">
.business-situation-scroll {
  :deep(.el-scrollbar__bar.is-vertical) {
    width: 10px;
  }
  :deep(.el-scrollbar__bar) {
    right: 0px;
  }
  :deep(.el-scrollbar__thumb) {
    background-color: var(--el-color-primary);
  }
}
</style>
