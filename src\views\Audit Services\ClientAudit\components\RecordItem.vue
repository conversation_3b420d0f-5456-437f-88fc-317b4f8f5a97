<template>
  <div class="client-record-item p-16px">
    <div class="time"> {{ source.time }} </div>
    <div class="flex w-[100%] p-16px">
      <div class="package-warp w-[70%]">
        <el-scrollbar class="menu-warp" style="height: 200px">
          <el-tooltip
            :content="item.package_name"
            v-for="(item, index) in filterPackageList"
            :key="index"
            :effect="'light'"
            :show-after="50"
            :offset="-10"
          >
            <div style="display: inline-block">
              <el-button
                size="large"
                :type="
                  source.checkedCode === item.package_code
                    ? 'success'
                    : item.isSubscribed
                    ? 'primary'
                    : 'info'
                "
                :disabled="!item.isSubscribed"
                class="w-[124px] mr-16px mb-8px menu-btn"
                @click="showMenu(item)"
                >{{ handleName(item.package_name) }}</el-button
              >
            </div>
          </el-tooltip>
        </el-scrollbar>
      </div>
      <el-scrollbar class="menu-warp w-[30%]" style="height: 200px" v-loading="menuLoading">
        <el-tree
          ref="treeRef"
          :data="source.menu"
          :expand-on-click-node="false"
          :default-expand-all="true"
          :props="defaultProps"
          highlight-current
          node-key="id"
        />
      </el-scrollbar>
    </div>
  </div>
</template>
<script setup name="ClientRecordItem" lang="ts">
const message = useMessage()
const props = defineProps({
  btnlist: {
    type: Array,
    default: () => []
  },
  data: {
    type: Object,
    default: () => {}
  }
})
const btnList = ref([])

/**------------------------------- 菜单树 ---------------------------------------*/
import { getListSimpleDepts } from '@/api/CustomList'

import { defaultProps, handleTree, listToTree } from '@/utils/tree'

// import { getCustomerAuditPackageDetail } from '@/api/Audit'
import { getCustomerAuditPackageDetail } from '../../common/api'

import { routeDuplication, filterRouteByCondition, remeberCatalog } from '@/utils/routerHelper'

/** 获得部门树 */
const getTree = async () => {
  const res = await getListSimpleDepts()
  deptList.value = []
  deptList.value.push(...handleTree(res))
  console.log('deptList', deptList.value)
  ;(!res || deptList.value.length > 0) &&
    deptList.value.unshift({
      children: undefined,
      id: -1,
      name: '未分配部门'
    })
}

import { getPackageMenu, getServiceMenu } from '@/api/Audit'

const menuLoading = ref(false)

const getMenu = async (_item) => {
  try {
    menuLoading.value = true
    let res = undefined
    if (_item.classes === '2') {
      res = await getServiceMenu({
        id: _item.package_id
      })
    } else {
      res = await getPackageMenu({
        id: _item.package_id
      })
    }
    console.log('res', res)
    const list = res.data.map((el) => {
      el.pid = el.parent_id
      return el
    })
    source.value.menu = handleMenu(listToTree(list))
  } finally {
    menuLoading.value = false
  }
}

const showMenu = (_item) => {
  if (_item.package_code === 'APIGW') {
    source.value.menu = []
    message.notifyWarning('暂不支持查看API市场详情!')
    return
  }
  // getTree()
  source.value.checkedCode = _item.package_code
  getMenu(_item)
}

const handleName = (_name) => {
  if (_name) {
    if (_name.length > 6) {
      return `${_name.slice(0, 6)}...`
    } else {
      return _name
    }
  } else {
    return ''
  }
}

const deptList = ref<any[]>([]) // 树形结构

// 模拟第一层级服务的信息
const transFormServiceMenu = (_menus) => {
  const serviceMap = {}
  _menus.forEach((el, index) => {
    if (serviceMap[el.application_id] === undefined) {
      serviceMap[el.application_id] = {
        children: [el],
        catalog: el.application_id,
        sort: Number(el.application_id)
      }
    } else {
      serviceMap[el.application_id].children.push(el)
    }
  })
  const serviceList = []
  for (const key in serviceMap) {
    serviceList.push(serviceMap[key])
  }
  return serviceList
}

const handleMenu = (_menus) => {
  const handledRoute = []
  const menus = transFormServiceMenu(_menus)
  menus.forEach((el) => {
    if (el.catalog && el.children) {
      el.children.forEach((item) => {
        // !item.path.startsWith('/') && (item.path = `/${item.path}`)
        // 记录父级的唯一值application_id，用作分服务排序
        item.parentCatalog = el.catalog
        item.parentCatalogSort = el.sort
        // 遍历首层让所有节点都记录上最外层服务的catalog和排序
        if (item.children && item.children.length > 0) {
          remeberCatalog(item.children, el.catalog, el.sort)
        }
      })
      handledRoute.push(...el.children)
    }
  })
  let resRoute = filterRouteByCondition(handledRoute)

  resRoute = routeDuplication(resRoute, menus)

  return resRoute
}

// 获取套餐详情
const getPackageDetail = async () => {
  const res = await getCustomerAuditPackageDetail({ id: 123 })
}

const source = ref({})

import { deepClone } from '@/utils/deep'

watch(
  () => props.data,
  (_newVal) => {
    if (_newVal) {
      source.value = deepClone(_newVal)
      const list = source.value.menu
        ? source.value.menu.map((el) => {
            el.pid = el.parent_id
            return el
          })
        : []
      source.value.menu = handleMenu(listToTree(list))
    }

    // console.log('listToTree(source.value.menu)', listToTree(source.value.menu))
    // source.value.menu = [...handleTree(source.value.menu)]
  },
  {
    deep: true,
    immediate: true
  }
)

import { excludeList } from '../../common/service'

const filterPackageList = computed(() => {
  return (
    source.value?.allPackageList.filter((el) => {
      return excludeList.indexOf(el.package_code)
    }) || []
  )
})
</script>
<style scoped lang="less">
.client-record-item {
  border: 1px solid #e5e7eb;
  border-radius: 5px;
  .time {
    font-size: 18px;
    color: gray;
  }

  :deep(.el-button + .el-button) {
    margin-left: 0px;
  }
  .menu-btn {
  }
}
</style>
