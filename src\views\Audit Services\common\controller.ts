import service from './service'

import { packageVo, customerAuditVo, pageVo } from './vo'

/**
 * 获取套餐
 */
const getPackage: (packageVo) => Promise<any[]> = async (_data: packageVo) => {
  return service.findAllPackageBySql(_data)
}

/**
 * 获取服务
 */
const getServer: (packageVo) => Promise<any[]> = async (_data: packageVo) => {
  return service.findAllCustomerServiceBySql(_data)
}

/**
 * 获取已经订阅的客户服务
 */
const getSubscribeCustomer: ({
  type,
  code
}: {
  type: string | number
  code: string | string[]
}) => Promise<any[]> = (_data: { type: string | number; code: string | string[] }) => {
  return service.findAllSubscribeCustomerBySql(_data)
}

// 获取套餐更新历史
const getPackageUploadRecord: ({
  type,
  code
}: {
  type: string | number
  code: string
}) => Promise<any[]> = (_data: { type: string | number; code: string }) => {
  return service.findAllPackageUploadRecordBySql(_data)
}

// 获取订阅客户总量
const getPackageAndCustomerServiceCustomerSum = (_data: { time?: any }) => {
  return service.findAllPackageAndCustomerServiceCustomerSum(_data)
}

// 获取所有套餐和定制服务的业务总量统计数据
const getPackageAndCustomerServiceBusinessSum = (_data: { time?: any }) => {
  return service.findAllPackageAndCustomerServiceBusinessSum(_data)
}

// 获取所有套餐和服务（没有时间）
const getAllPackageAndCustomerService = () => {
  return service.findAllPackageAndCustomerService({ time: undefined })
}

/**
 * 获取某个套餐和定制服务的业务总量统计数据
 */
const getPackageAndCustomerServiceBusinessSumByCode = (_data: { code: string; time?: any }) => {
  return service.findPackageAndCustomerServiceBusinessSum(_data)
}

// 获取所有套餐和服务（根据时间）
const getPackageAndCustomerService = (_data: { time?: any }) => {
  return service.findAllPackageAndCustomerService(_data)
}

// 获取首屏展示完整列表
const getPackageAndServiceFirstShow = (list) => {
  return service.getPackageAndServiceFirstShow(list)
}

/**
 * 根据时间范围获取业务套餐数据（按月）
 */
const getBusinessDataRange = (_data: { code: string; time?: any }) => {
  return service.findBusinessDataRange(_data)
}

/**
 * 根据时间范围获取客户总量（按月）
 */
const getCustomerSumRange = (_data: { time?: any }) => {
  return service.findCustomerSumRange(_data)
}

const getBusinessDataRangeSum = (_data: { time?: any }) => {
  return service.findBusinessDataRangeSum(_data)
}

// 获取客户审计记录
const getCustomerAuditRecord = (_page: pageVo, _data: customerAuditVo) => {
  return service.findCustomerAuditRecord(_page, _data)
}

export default {
  getPackage,
  getServer,
  getSubscribeCustomer,
  getPackageUploadRecord,
  getPackageAndCustomerServiceCustomerSum,
  getPackageAndCustomerServiceBusinessSum,
  getAllPackageAndCustomerService,
  getPackageAndCustomerServiceBusinessSumByCode,
  getPackageAndCustomerService,
  getBusinessDataRange,
  getCustomerSumRange,
  getBusinessDataRangeSum,
  getCustomerAuditRecord,
  getPackageAndServiceFirstShow
}
