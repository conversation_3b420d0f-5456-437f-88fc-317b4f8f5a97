import { forEach } from './../../../utils/tree'
import auditApi from './api'
import { packageVo, packageTo, customerAuditVo, pageVo } from './vo'
import { getTimeSqlFragment, generateMonthDict } from './utils'
import { deepClone } from '@/utils/deep'
import controller from './controller'
import { yearDateRangesMap } from '../AuditReport/utils/yearDateRange'
import { qoqDateRangesMap } from '../AuditReport/utils/qoqDateRange'
import { calculateChange } from '../AuditReport/utils'

// 业务要求以下套餐不应出现在审计数据中
export const excludeList = [
  'doubts_in_image_review_application', //图审存疑
  'base_comp_application', //基础运维
  'standard_comp_application', //标准运维
  'workflow_application', //工作流程
  'pay_mgr_application', //支付管理
  'standard_comp_application_international', //标准运维_国际版
  'standard_comp_application_hk', //标准运维_国际版
  'client_test' //内部测试用的套餐
]

// 业务要求以下套餐业务数据置为0
const fakeNumList = [
  'intelligent_outbound_call_application', //智能外呼
  'image_processing_application' //图像处理
]

// 获取通用套餐
const findAllPackageBySql: (packageVo) => Promise<any[]> = async (_data: packageVo) => {
  const data: packageVo = deepClone(_data)
  data.date_val = getTimeSqlFragment({ time: data.date_val, isOrigin: false })
  const originList = (await auditApi.getPackage(data))?.data.filter((el) => {
    return excludeList.indexOf(el.code) == -1
  })

  if (originList.length > 0) {
    const packageResArr: any = [] //通过地址指针的方式赋值
    const customerRequestList: any = (
      await auditApi.getPackageCustomerSubList({
        date_val: getTimeSqlFragment({})
      })
    )?.data
    const businessRequestList: any = []

    const customerRequestMap = customerRequestList.reduce((a, b) => {
      return {
        ...a,
        [b.pck_code]: b.count
      }
    }, {})

    for (let i = 0; i < originList.length; i++) {
      if (auditApi.getBusinessNumGeneral[originList[i].code]) {
        businessRequestList.push(auditApi.getBusinessNumGeneral[originList[i].code].sql())
        packageResArr.push(originList[i])
      }
    }

    // 业务数据
    const businessAllRes: any = await Promise.allSettled(businessRequestList)

    for (let index = 0; index < originList.length; index++) {
      if (customerRequestMap[originList[index].code]) {
        originList[index].customer_num = customerRequestMap[originList[index].code]
      } else {
        originList[index].customer_num = 0
      }
    }

    for (let index = 0; index < packageResArr.length; index++) {
      if (businessAllRes[index] && businessAllRes[index].status == 'fulfilled') {
        console.log('businessAllRes', businessAllRes)
        const busRes = businessAllRes[index].value.data

        if (fakeNumList.indexOf(packageResArr[index].code) != -1) {
          packageResArr[index].business_num = 0
        } else {
          packageResArr[index].business_num = Number(busRes['count'])
        }
      } else {
        packageResArr[index].business_num = undefined
      }
    }
  }
  return originList
}

// 获取定制服务
const findAllCustomerServiceBySql: (packageVo) => Promise<any[]> = async (_data: packageVo) => {
  const data: packageVo = deepClone(_data)
  data.date_val = getTimeSqlFragment({ time: data.date_val, isOrigin: false })
  const originList = (await auditApi.getService(data))?.data.filter((el) => {
    return excludeList.indexOf(el.code) == -1
  })
  if (originList.length > 0) {
    const packageResArr: any = [] //通过地址指针的方式赋值
    const customerRequestList: any = (
      await auditApi.getServiceCustomerSubList({
        date_val: getTimeSqlFragment({ isOrigin: true })
      })
    )?.data
    const businessRequestList: any = []

    for (let i = 0; i < originList.length; i++) {
      if (auditApi.getBusinessNumGeneral[originList[i].code]) {
        businessRequestList.push(auditApi.getBusinessNumGeneral[originList[i].code].sql())
        packageResArr.push(originList[i])
      }
    }

    // 业务数据
    const businessAllRes: any = await Promise.allSettled(businessRequestList)

    const customerRequestMap = customerRequestList.reduce((a, b) => {
      return {
        ...a,
        [b.code]: b
      }
    }, {})

    console.log('customerRequestMap5656', customerRequestMap)

    for (let index = 0; index < originList.length; index++) {
      if (customerRequestMap[originList[index].code]) {
        originList[index].customer_num = customerRequestMap[originList[index].code].tenantIdCount
        originList[index].customer_list = customerRequestMap[originList[index].code].tenantIds
      } else {
        originList[index].customer_num = 0
      }
    }

    for (let index = 0; index < packageResArr.length; index++) {
      if (businessAllRes[index] && businessAllRes[index].status == 'fulfilled') {
        const busRes = businessAllRes[index].value.data

        if (fakeNumList.indexOf(packageResArr[index].code) != -1) {
          packageResArr[index].business_num = 0
        } else {
          packageResArr[index].business_num = Number(busRes['count'])
        }
      } else {
        packageResArr[index].business_num = undefined
      }
    }
  }
  return originList
}

/**
 * 获取已经订阅的客户
 */
const findAllSubscribeCustomerBySql = async (_data: {
  type: string | number
  code: string | string[]
}) => {
  if (_data.type === '1' || _data.type === 1) {
    return auditApi.getPackageSubscribeCustomer({ code: _data.code as string })
  } else {
    return auditApi.getServiceSubscribeCustomer({ code: _data.code as string })
  }
}

// 获取变更记录
const findAllPackageUploadRecordBySql = async (_data: { type: string | number; code: string }) => {
  if (_data.type === '1' || _data.type === 1) {
    return auditApi.getPackageUploadRecord({ code: _data.code as string })
  } else {
    return auditApi.getServiceUploadRecord({ code: _data.code as string })
  }
}

import { useCache } from '@/hooks/web/useCache'

const { wsCache } = useCache()

/**
 * 获取所有的套餐和定制服务（不分时间, 加缓存）
 */
const findAllPackageAndCustomerServiceWhitoutTime = async () => {
  try {
    if (wsCache.get('allPakSer')) {
      return wsCache.get('allPakSer')
    }
    const list = (await auditApi.getAllPakSer()).data.filter((el) => {
      return excludeList.indexOf(el.code) == -1
    })
    wsCache.set('allPakSer', list, { exp: 300 })
    return list
  } catch (e) {
    console.log('e', e)
  }
}

// 获取所有套餐和定制服务
const findAllPackageAndCustomerService = async (_data: { time?: any }) => {
  try {
    const originList = await findAllPackageAndCustomerServiceWhitoutTime()
    if (originList.length > 0) {
      const packageResArr: any = [] //通过地址指针的方式赋值
      const pkgCustomerRequestList: any = (
        await auditApi.getPackageCustomerSubList({
          date_val: getTimeSqlFragment({ time: _data.time })
        })
      )?.data

      const serCustomerRequestList: any = (
        await auditApi.getServiceCustomerSubList({
          date_val: getTimeSqlFragment({ time: _data.time, isOrigin: true })
        })
      )?.data

      const businessRequestList: any = []

      for (let i = 0; i < originList.length; i++) {
        if (auditApi.getBusinessNumGeneral[originList[i].code]) {
          businessRequestList.push(
            auditApi.getBusinessNumGeneral[originList[i].code].sql({
              date_val: getTimeSqlFragment({ time: _data.time })
            })
          )
          packageResArr.push(originList[i])
        }
      }

      // 业务数据
      const businessAllRes: any = await Promise.allSettled(businessRequestList)

      const pkgCustomerRequestMap = pkgCustomerRequestList.reduce((a, b) => {
        return {
          ...a,
          [b.pck_code]: b.count
        }
      }, {})

      const serCustomerRequestMap = serCustomerRequestList.reduce((a, b) => {
        return {
          ...a,
          [b.code]: b
        }
      }, {})

      for (let index = 0; index < originList.length; index++) {
        if (originList[index].classes === '1' || originList[index].classes === 1) {
          //通用套餐
          if (pkgCustomerRequestMap[originList[index].code]) {
            originList[index].customer_num = pkgCustomerRequestMap[originList[index].code]
          } else {
            originList[index].customer_num = 0
          }
        } else {
          if (serCustomerRequestMap[originList[index].code]) {
            //定制服务
            originList[index].customer_num =
              serCustomerRequestMap[originList[index].code].tenantIdCount
            originList[index].customer_list =
              serCustomerRequestMap[originList[index].code].tenantIds
          } else {
            originList[index].customer_num = 0
          }
        }
      }

      for (let index = 0; index < packageResArr.length; index++) {
        if (businessAllRes[index] && businessAllRes[index].status == 'fulfilled') {
          const busRes = businessAllRes[index].value.data

          if (fakeNumList.indexOf(packageResArr[index].code) != -1) {
            packageResArr[index].business_num = 0
          } else {
            packageResArr[index].business_num = Number(busRes['count'])
          }
        } else {
          packageResArr[index].business_num = undefined
        }
      }
    }
    return originList
  } catch (e) {}
}

// 获取所有套餐和定制服务的客户总量统计数据
const findAllPackageAndCustomerServiceCustomerSum = async (_data: { time?: string | Object }) => {
  try {
    _data.time = getTimeSqlFragment({
      time: _data.time,
      hour: 0,
      isOrigin: true
    })
    return auditApi.getPackageAndCustomerServiceCustomerSum({ date_val: _data.time })
  } catch (e) {}
}

// 获取所有套餐和定制服务的业务总量统计数据
const findAllPackageAndCustomerServiceBusinessSum = async (_data: { time?: string | Object }) => {
  try {
    const originList = await findAllPackageAndCustomerServiceWhitoutTime()
    if (originList.length > 0) {
      const packageResArr: any = [] //通过地址指针的方式赋值

      const businessRequestList: any = []

      for (let i = 0; i < originList.length; i++) {
        if (auditApi.getBusinessNumGeneral[originList[i].code]) {
          businessRequestList.push(
            auditApi.getBusinessNumGeneral[originList[i].code].sql({
              date_val: getTimeSqlFragment({ time: _data.time })
            })
          )
          packageResArr.push(originList[i])
        }
      }

      // 业务数据
      const businessAllRes: any = await Promise.allSettled(businessRequestList)

      let sumCount = 0

      for (let index = 0; index < packageResArr.length; index++) {
        if (businessAllRes[index] && businessAllRes[index].status == 'fulfilled') {
          console.log('businessAllRes', businessAllRes)
          const busRes = businessAllRes[index].value.data

          if (fakeNumList.indexOf(packageResArr[index].code) != -1) {
            packageResArr[index].business_num = 0
          } else {
            packageResArr[index].business_num = Number(busRes['count'])
            sumCount += Number(busRes['count'])
          }
        } else {
          packageResArr[index].business_num = undefined
        }
      }
      return sumCount
    }
  } catch (e) {
    return 0
  }
}

/**
 * 获取某个套餐和定制服务的业务总量统计数据
 */
const findPackageAndCustomerServiceBusinessSum = async (_data: { code: string; time?: any }) => {
  if (auditApi.getBusinessNumGeneral[_data.code]) {
    return (
      await auditApi.getBusinessNumGeneral[_data.code].sql({
        date_val: getTimeSqlFragment({ time: _data.time })
      })
    ).data.count
  } else {
    return undefined
  }
}

/**
 * 根据时间范围获取业务套餐数据（按月）
 */
const findBusinessDataRange = async (_data: { code: string; time?: any }) => {
  if (auditApi.getBusinessNumGeneral[_data.code]) {
    const res = (
      await auditApi.getBusinessNumGeneral[_data.code].rangeSql({
        date_val: getTimeSqlFragment({ time: _data.time, hour: 0 })
      })
    ).data
    const timesMap: any = generateMonthDict(_data.time)
    res.forEach((el) => {
      timesMap[el.month] = el.count
    })
    const list: any = []
    for (const key in timesMap) {
      list.push({
        value: timesMap[key],
        name: key
      })
    }
    return list
  } else {
    return []
  }
}

import { getLastDayOfMounth } from '@/utils/formatTime'

/**
 * 根据时间范围获取客户总量（按月）
 */
const findCustomerSumRange = async (_data: { time?: any }) => {
  try {
    _data.time = getTimeSqlFragment({
      time: _data.time,
      hour: 0,
      searchStr: 'sub.update_time',
      isOrigin: true
    })
    const res = (
      await auditApi.getPackageAndCustomerServiceCustomerSumRange({ date_val: _data.time })
    ).data
    const times =
      Object.prototype.toString.call(_data.time) === '[object String]'
        ? [`${_data.time}-01-01`, `${_data.time}-12-${getLastDayOfMounth(_data.time, 12)}`]
        : _data.time
    const timesMap = generateMonthDict(times)

    console.log('timesMap', timesMap)

    res.forEach((el) => {
      timesMap[el.month] = el.count
    })

    const list: any = []
    for (const key in timesMap) {
      list.push({
        value: timesMap[key],
        name: key
      })
    }
    return list
  } catch (e) {
    console.error('e', e)
  }
}

const findBusinessDataRangeSum = async (_data: { time?: any }) => {
  const originList = await findAllPackageAndCustomerServiceWhitoutTime()
  if (originList.length > 0) {
    const times =
      Object.prototype.toString.call(_data.time) === '[object String]'
        ? [`${_data.time}-01-01`, `${_data.time}-12-${getLastDayOfMounth(_data.time, 12)}`]
        : _data.time
    const timesMap = generateMonthDict(times)

    const packageResArr: any = [] //通过地址指针的方式赋值
    const businessRequestList: any = []

    _data.time = getTimeSqlFragment({
      time: _data.time
      // hour: 0
    })

    for (let i = 0; i < originList.length; i++) {
      if (auditApi.getBusinessNumGeneral[originList[i].code]) {
        businessRequestList.push(
          auditApi.getBusinessNumGeneral[originList[i].code].rangeSql({ date_val: _data.time })
        )
        packageResArr.push(originList[i])
      }
    }

    // 业务数据
    const businessAllRes: any = await Promise.allSettled(businessRequestList)

    for (let index = 0; index < packageResArr.length; index++) {
      if (businessAllRes[index] && businessAllRes[index].status == 'fulfilled') {
        const busRes = businessAllRes[index].value.data
        busRes.forEach((el, i) => {
          timesMap[el.month] += Number(el.count)
        })
      }
    }

    const arrList: any = []
    for (const key in timesMap) {
      arrList.push({
        value: timesMap[key],
        name: key
      })
    }

    const result = arrList.filter((_item) => {
      return typeof _item.value === 'number'
    })

    return result
  } else {
    return []
  }
}

// 获取客户审计记录
const findCustomerAuditRecord = async (_page: pageVo, _data: customerAuditVo) => {
  // 有的sql对应拼接的code不一样，有的是租户id，有的是客户id
  const codeTypeMap = {
    customerId: 'customer_id',
    tenantId: 'tenant_id'
  }
  const list = (await auditApi.getCustomerAuditRecord(_page, _data)).data
  if (list.length > 0) {
    const taskResArr: any = [] //通过地址指针的方式赋值
    const businessRequestList: any = []

    for (let i = 0; i < list.length; i++) {
      if (auditApi.getCustomBusinessNumGeneral[list[i].package_code]) {
        const codeType = auditApi.getCustomBusinessNumGeneral[list[i].package_code].codeType
        businessRequestList.push(
          auditApi.getCustomBusinessNumGeneral[list[i].package_code].sql({
            code: list[i][codeTypeMap[codeType]]
          })
        )
        taskResArr.push(list[i])
      }
    }

    // 业务数据
    const businessAllRes: any = await Promise.allSettled(businessRequestList)

    for (let index = 0; index < taskResArr.length; index++) {
      if (businessAllRes[index] && businessAllRes[index].status == 'fulfilled') {
        const busRes = businessAllRes[index].value.data.count
        if (fakeNumList.indexOf(taskResArr[index].code) != -1) {
          taskResArr[index].business_num = 0
        } else {
          if (busRes !== undefined && busRes !== null) {
            taskResArr[index].business_num = Number(busRes)
          }
        }
      } else {
        taskResArr[index].business_num = undefined
      }
    }
  }
  return list
}

/**
 * 获取首屏展示完整列表
 * @param list 从上层传过来，包含客户订阅数和业务数据的套餐列表
 * 目标是将所有的套餐都加上年同比和季环比
 * */
const getPackageAndServiceFirstShow = async (list) => {
  // 年和季比都分时间1和时间2，才能进行比较，靠后的定义为next，靠前的定义为pre
  const yearSelectPreResList: any = []
  const yearSelectNextResList: any = []
  const qoqSelectPreResList: any = []
  const qoqSelectNextResList: any = []

  // 获取年同比和季环比的选中状态参数
  const selectedRageYear = yearDateRangesMap['year']
  const selectedRageQoq = qoqDateRangesMap['quarter']

  // 作为指针列表，记录下跳过匹配code的promise请求和原数组套餐item的对应位置，在跳过没有匹配上code的列表上，可以根据index直接对应指针地址修改原数组
  const handleList: any = []

  for (let i = 0; i < list.length; i++) {
    const code = list[i].code
    if (auditApi.getBusinessNumGeneral[code]) {
      // 记录对应的套餐item
      handleList.push(list[i])
      // 年同比pre
      yearSelectPreResList.push(
        controller.getPackageAndCustomerServiceBusinessSumByCode({
          time: selectedRageYear[1],
          code: code
        })
      )
      // 年同比next
      yearSelectNextResList.push(
        controller.getPackageAndCustomerServiceBusinessSumByCode({
          time: selectedRageYear[0],
          code: code
        })
      )
      // 半年环比pre
      qoqSelectPreResList.push(
        controller.getPackageAndCustomerServiceBusinessSumByCode({
          time: selectedRageQoq[1],
          code: code
        })
      )
      // 半年环比next
      qoqSelectNextResList.push(
        controller.getPackageAndCustomerServiceBusinessSumByCode({
          time: selectedRageQoq[0],
          code: code
        })
      )
    }
  }

  // 年同比pre
  const yearSelectPreRes: any = await Promise.allSettled(yearSelectPreResList)
  // 年同比next
  const yearSelectNextRes: any = await Promise.allSettled(yearSelectNextResList)
  // 半年环比pre
  const qoqSelectPreRes: any = await Promise.allSettled(qoqSelectPreResList)
  // 半年环比next
  const qoqSelectNextRes: any = await Promise.allSettled(qoqSelectNextResList)

  for (let index = 0; index < handleList.length; index++) {
    const obj = {
      year_pre: 0,
      year_next: 0,
      qoq_pre: 0,
      qoq_next: 0
    }
    // 年同比pre
    if (yearSelectPreRes[index] && yearSelectPreRes[index].status == 'fulfilled') {
      const res = yearSelectPreRes[index].value
      obj.year_pre = res
    }
    // 年同比next
    if (yearSelectNextRes[index] && yearSelectNextRes[index].status == 'fulfilled') {
      const res = yearSelectNextRes[index].value
      obj.year_next = res
    }
    // 半年环比pre
    if (qoqSelectPreRes[index] && qoqSelectPreRes[index].status == 'fulfilled') {
      const res = qoqSelectPreRes[index].value
      obj.qoq_pre = res
    }
    // 半年环比next
    if (qoqSelectNextRes[index] && qoqSelectNextRes[index].status == 'fulfilled') {
      const res = qoqSelectNextRes[index].value
      obj.qoq_next = res
    }
    handleList[index].firstShowCompareInfo = obj
  }

  return list
}

export default {
  findAllPackageBySql,
  findAllCustomerServiceBySql,
  findAllSubscribeCustomerBySql,
  findAllPackageUploadRecordBySql,
  findAllPackageAndCustomerServiceBusinessSum,
  findAllPackageAndCustomerServiceWhitoutTime,
  findAllPackageAndCustomerServiceCustomerSum,
  findAllPackageAndCustomerService,
  findPackageAndCustomerServiceBusinessSum,
  findBusinessDataRange,
  findCustomerSumRange,
  findBusinessDataRangeSum,
  findCustomerAuditRecord,
  getPackageAndServiceFirstShow
}
