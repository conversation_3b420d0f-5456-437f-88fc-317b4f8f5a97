<template>
  <div
    class="distribution-sector-chart-1 bg-white p-16px"
    style="height: 49%; border-radius: 4px; box-shadow: var(--el-box-shadow-light)"
    v-loading="loading && !amplifyDialogIsShow"
  >
    <div style="height: 100%">
      <div class="flex flex-between">
        <p>{{ title }}</p>
        <YearSelect ref="YearSelectRef" @get-data="getYear" />
      </div>
      <div class="chart-warp"> </div>
      <echart
        ref="echartRef"
        :options="option"
        :width="width"
        :height="height"
        @click="checkDetail"
      />
      <div class="flex" style="justify-content: space-around; flex-direction: row-reverse">
        <div
          class="quantity-warp w-3/8 b-32px mt-8px"
          v-for="(item, index) in data"
          :key="index"
          v-show="data.length <= 3 || index + 1 > data.length - 3"
        >
          <div class="flex" style="justify-content: center; align-items: center">
            <div
              class="round w-10px h-10px mr-8px"
              :style="{ borderRadius: '50%', background: item.itemStyle?.color }"
            >
            </div>
            <div class="title" style="font-size: 16px; max-width: 140px">{{ item.name }}</div>
          </div>
          <div class="values" style="text-align: center; font-size: 20px; color: grey">
            {{ item.value === undefined ? '--' : item.value }}
            <span class="text-xs" v-if="item.value !== undefined">{{ getUnit(item.code) }}</span>
          </div>
        </div>
      </div>
      <amplifyDialog ref="amplifyDialogRef" @change-time="amplifyChangeTime" />
    </div>
  </div>
</template>

<script setup name="DataSector" lang="ts">
import YearSelect from '../Search/YearSelect.vue'
import { generateDarkColors } from '@/utils/generateDarkColors'
import amplifyDialog from '../amplifyDialog.vue'
import { getPackageAndCustomerService } from '@/api/Audit'
import { useUnit } from '../../hooks/useUnit'

const { getUnit } = useUnit()

const props = defineProps({
  tenantList: {
    type: Array,
    default: () => []
  }
})

const title = ref('客户业务分布图')

const amplifyDialogRef = ref()

const width = ref('100%')
const height = ref('70%')

const YearSelectRef = ref()

const year = ref('')

import { getLastDayOfMounth, addHours } from '@/utils/formatTime'

import { getSumTimeSql } from '../../../common/sql'

import controller from '../../../common/controller'

const times = ref()

const getYear = () => {
  console.log('YearSelectRef.value.year', YearSelectRef.value.year)
  year.value = YearSelectRef.value.year
  times.value = YearSelectRef.value.year
  getDataFn()
}

// const option = computed(() => {
//   return {
//     tooltip: {
//       trigger: 'item',
//       formatter: '{b} <br /> {c}人, 占{d}%'
//     },
//     grid: {
//       left: '3%',
//       right: '4%',
//       bottom: '3%',
//       top: '15%',
//       containLabel: false,
//       width: '50%',
//       height: '50%'
//     },
//     // legend: {
//     //   top: 'bottom'
//     // },
//     series: [
//       {
//         type: 'pie',
//         radius: [10, 100],
//         center: ['50%', '50%'],
//         roseType: 'area',
//         itemStyle: {
//           borderRadius: 4
//         },
//         label: {
//           show: false
//         },
//         data: data.value
//       }
//     ]
//   }
// })

const option = computed(() => {
  const maxNum = data.value && data.value.length > 0 ? data.value[data.value.length - 1].value : 0 //计算最大值

  return {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      max: Math.floor(maxNum * 1.1)
    },
    yAxis: [
      {
        type: 'category',
        data: data.value.map((el) => el.name),
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    series: [
      {
        type: 'bar',
        barWidth: '60%',
        data: data.value
      }
    ],
    dataZoom:
      data.value.length > 10
        ? [
            {
              type: 'slider',
              orient: 'vertical',
              yAxisIndex: 0,
              start: 1000,
              end: 990,
              minValueSpan: 7,
              width: 10,
              showDetail: false
            },
            {
              type: 'inside',
              orient: 'vertical'
            }
          ]
        : []
  }
})

// 打开大图
const checkDetail = () => {
  const obj = deepClone(option.value)
  if (data.value.length > 8) {
    obj.dataZoom[0].width = 20
  }
  obj.series[0].label = {
    show: true,
    position: 'right'
  }
  amplifyDialogRef.value.open(obj, title.value, {
    type: 'year',
    times: times.value
  })
}

const data = ref([])

import { colors } from '../colors'
import { deepClone } from '@/utils/deep'

const loading = ref(false)

const getDataFn = async (_list?) => {
  try {
    loading.value = true
    let list: any = []
    if (_list) {
      // 首屏渲染，统一从外面获取数据
      list = deepClone(_list)
    } else {
      list = await controller.getPackageAndCustomerService({
        time: year.value
      })
    }

    data.value = list.map((el) => {
      el.value = el.customer_num
      return el
    })

    data.value = data.value.sort((a, b) => {
      let aNum = 0
      let bNum = 0
      aNum = a.value
      bNum = b.value
      if (a.value === undefined) {
        aNum = 0
      }
      if (b.value === undefined) {
        bNum = 0
      }

      return aNum - bNum
    })

    if (data.value.length <= colors.length) {
      data.value.map((el, index) => {
        el.itemStyle = {
          color: colors[index]
        }
      })
    } else {
      const _colors = generateDarkColors(data.value.length, colors)
      data.value.map((el, index) => {
        el.itemStyle = {
          color: _colors[data.value.length - index - 1]
        }
      })
    }
    // data.value = data.value.sort((a, b) => {
    //   return b.value - a.value
    // })
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // nextTick(() => {
  //   getYear()
  // })
})

// watch(
//   () => props.tenantList,
//   () => {
//     nextTick(() => {
//       getYear()
//     })
//   },
//   {
//     deep: true,
//   }
// )

const amplifyChangeTime = (_time) => {
  YearSelectRef.value.year = _time
  getYear()
}

watch(
  () => data.value,
  (_newVal) => {
    if (amplifyDialogRef.value.show) {
      checkDetail()
      amplifyDialogRef.value.loading = false
    }
  },
  {
    deep: true
  }
)

const amplifyDialogIsShow = computed(() => {
  return amplifyDialogRef.value.show || false
})

defineExpose({
  loading,
  getDataFn
})
</script>

<style scoped lang="less">
.flex-between {
  justify-content: space-between;
}
</style>
