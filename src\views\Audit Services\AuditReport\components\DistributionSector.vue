<!-- 分布板块 -->
<template>
  <div
    class="distribution-sector h-full p-16px flex flex-between flex-col"
    style="background: ghostwhite"
    ><DistributionSectorChart1
      :tenant-list="allTenantList"
      ref="chart1Ref" /><DistributionSectorChart2 :tenant-list="allTenantList" ref="chart2Ref"
  /></div>
</template>

<script setup name="DataSector" lang="ts">
import DistributionSectorChart1 from '../Echart/DistributionSector/Chart1.vue'
import DistributionSectorChart2 from '../Echart/DistributionSector/Chart2.vue'

import controller from '../../common/controller'

import dayjs from 'dayjs'

const allTenantList = ref([])

const chart1Ref = ref()
const chart2Ref = ref()

/**时间段,默认今年以来 */
const timePeriod: Ref<String[]> = ref([
  dayjs(new Date(new Date().getFullYear(), 0)).format('YYYY-MM'),
  dayjs(new Date()).format('YYYY-MM')
])

import { deepClone } from '@/utils/deep'

// 首屏渲染获取所有的套餐数据，减少请求
const getDataFn = async (_list?) => {
  try {
    let list: any = []
    if (_list) {
      list = deepClone(_list)
    } else {
      list = await controller.getPackageAndCustomerService({
        time: timePeriod.value
      })
    }
    // 首屏在同年下获取回来的数据相同，可以复用同一套数据
    chart1Ref.value.getDataFn(list)
    chart2Ref.value.getDataFn(list)
  } finally {
    chart1Ref.value.loading = false
    chart2Ref.value.loading = false
  }
}

onMounted(() => {
  chart1Ref.value.loading = true
  chart2Ref.value.loading = true
  // getDataFn()
})

defineExpose({
  getDataFn
})
</script>

<style scoped lang="less">
.flex-between {
  justify-content: space-between;
}
</style>
