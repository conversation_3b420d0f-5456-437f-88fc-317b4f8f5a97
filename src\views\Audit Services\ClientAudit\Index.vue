<!--客户审计-->
<template>
  <div>
    <ContentWrap>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="客户名称" prop="styleLevel">
          <CustomerSelect
            v-model="queryParams.customerId"
            :customer-code="queryParams.tenantId"
            clearable
          />
          <!-- <el-select v-model="queryParams.customerName" clearable placeholder="请选择客户">
            <el-option
              v-for="item in appList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select> -->
        </el-form-item>
        <el-form-item label="套餐名称">
          <el-autocomplete
            placeholder="请输入套餐名称"
            :maxlength="10"
            v-model="queryParams.name"
            :fetch-suggestions="querySearch"
            clearable
          />
        </el-form-item>
        <el-form-item label="订阅时间">
          <el-date-picker
            v-model="queryParams.time"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
            end-placeholder="结束日期"
            start-placeholder="开始日期"
            type="daterange"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-track:click.btn @click="onSearch">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <ContentWrap>
      <el-table
        v-loading="loading"
        :data="tableData"
        :header-cell-style="{ background: '#F7F7F9', color: '#606266' }"
        style="width: 100%; margin-top: 20px"
      >
        <el-table-column type="index" label="序号" width="60" align="center" headerAlign="center">
          <template #default="scope">{{ scope.$index + 1 + pageSize * (pageNum - 1) }}</template>
        </el-table-column>
        <el-table-column label="客户名称" align="center" width="300" prop="tenant_name" />
        <el-table-column label="已订套餐" align="center" prop="package_name">
          <template #default="{ row }">
            <el-button type="primary" text @click="toDetail(row)">{{ row.package_name }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="订单/任务总数" align="center" prop="business_num" />
        <!-- <el-table-column label="订单/任务金额" align="center" prop="customerName" /> -->
        <el-table-column label="订阅时间" align="center" prop="customerName">
          <template #default="{ row }">
            {{ row.create_time ? formatDate(row.create_time, 'YYYY-MM-DD HH:mm:ss') : '' }}
          </template>
        </el-table-column>
        <el-table-column label="订阅到期时间" align="center" width="300" prop="expire_time" />
        <!-- <el-table-column label="订阅到期时间（暂无）" align="center" prop="customerName" />
        <el-table-column label="订阅时长（暂无）" align="center" prop="customerName" />
        <el-table-column label="订阅费用（暂无）" align="center" prop="customerName" /> -->
        <el-table-column label="订阅变更时间" align="center" prop="customerName">
          <template #default="{ row }">
            {{ row.create_time ? formatDate(row.update_time, 'YYYY-MM-DD HH:mm:ss') : '' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" fixed="right" width="160">
          <template #default="{ row }">
            <el-button type="primary" link @click="showChangeRecord(row)">变更记录</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页工具条 -->
      <Pagination
        class="bottom-view"
        v-model:page="pageNum"
        v-model:limit="pageSize"
        :total="total"
        @pagination="handleQuery"
      />
    </ContentWrap>
  </div>
</template>

<script setup name="ClientAudit" lang="ts">
// import { getCustomerAuditRecord, getCustomerAuditRecordCount } from '@/api/Audit'
import { getCustomerAuditRecord, getCustomerAuditRecordCount } from '../common/api'
import { formatDate } from '@/utils/formatTime'

import controller from '../common/controller'

const queryFormRef = ref(ElForm)

const queryParams = ref({
  customerId: undefined,
  name: undefined,
  time: undefined,
  tenantId: undefined
})

const appList = [
  {
    value: 1,
    label: '测试'
  }
]

const pageNum = ref(1)

const pageSize = ref(10)

const total = ref(0)

const handleQuery = async () => {
  const obj = queryParams.value.customerId
    ? {
        customerId: queryParams.value.customerId,
        name: queryParams.value.name,
        time: queryParams.value.time
      }
    : deepClone(queryParams.value)
  try {
    loading.value = true

    // 并行请求数据和总数
    const [dataRes, countRes] = await Promise.all([
      controller.getCustomerAuditRecord(
        {
          pageNum: pageNum.value,
          pageSize: pageSize.value
        },
        obj
      ),
      getCustomerAuditRecordCount(obj)
    ])

    tableData.value = dataRes
    total.value = countRes.data.total || countRes.data
  } finally {
    loading.value = false
  }
}

const onSearch = () => {
  pageNum.value = 1
  handleQuery()
}

const onReset = () => {
  queryParams.value = {
    customerId: undefined,
    name: undefined,
    time: undefined
  }
  handleQuery()
}

const router = useRouter()

const loading = ref(false)

const tableData = ref([])

import { getAllPackageAndCustomerService } from '@/api/Audit'
import { deepClone } from '@/utils/deep'

const allPackageAndServiceList = ref([])

const getSearchItem = async () => {
  try {
    const res = await getAllPackageAndCustomerService()
    allPackageAndServiceList.value = res.data.map((el) => {
      el.value = el.name
      return el
    })
  } catch (e) {
    allPackageAndServiceList.value = []
  }
}

const createFilter = (queryString: string) => {
  return (_list) => {
    return _list.value.toLowerCase().indexOf(queryString.toLowerCase()) != -1
  }
}

const querySearch = (queryString: string, cb: any) => {
  const results = queryString
    ? allPackageAndServiceList.value.filter(createFilter(queryString))
    : allPackageAndServiceList.value
  cb(results)
}

const showChangeRecord = (_row) => {
  router.push({
    name: 'ClientAuditRecords',
    query: {
      tid: _row.tenant_id,
      tn: _row.tenant_name
    }
  })
}

const route = useRoute()

const tid = computed(() => {
  return route.query.tid
})

const tn = computed(() => {
  return route.query.tn
})

const getData = async () => {
  const res = await getCustomerAuditRecord(
    { pageNum: pageNum.value, pageSize: pageSize.value },
    queryParams.value
  )
}

const toDetail = (_row) => {
  router.push({
    name: 'BusinessAuditIndex',
    query: {
      pn: _row.package_name,
      classes: _row.classes
    }
  })
}

onMounted(() => {
  if (tid.value) {
    queryParams.value.tenantId = tid.value
  }
  getSearchItem()
  handleQuery()
})
</script>

<style scoped lang="less"></style>
